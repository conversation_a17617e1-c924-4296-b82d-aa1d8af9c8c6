<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 标签页样式 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 徽章样式 */
        .badge-count {
            background-color: #f1f5f9;
            color: #64748b;
        }
        .badge-count-active {
            background-color: #dbeafe;
            color: #2563eb;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }
        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }
        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }
        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }
        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .custom-select-search input:focus {
            border-color: #3b82f6;
        }
        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }
        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }
        .custom-select-option:hover {
            background-color: #f3f4f6;
        }
        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }
        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 消息提示样式 */
        #messageContainer {
            z-index: 1000;
        }

        /* 批量权限设置模态框样式 */
        .modal-left-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .modal-content-area {
            background: #ffffff;
        }

        /* 权限卡片样式 */
        .permission-card {
            position: relative;
        }

        .permission-card input[type="radio"]:checked + .permission-card-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }

        .permission-card input[type="radio"]:checked ~ .permission-card-check {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #3b82f6;
        }

        .permission-card input[type="radio"]:checked ~ .permission-card-check i {
            opacity: 1;
        }

        .permission-card:hover .permission-card-check {
            border-color: #3b82f6;
        }

        /* 权限值卡片样式 */
        .permission-value-card {
            position: relative;
        }

        .permission-value-card input[type="radio"]:checked + .permission-value-icon {
            transform: scale(1.05);
        }

        .permission-value-card input[type="radio"]:checked ~ .permission-value-check {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #3b82f6;
        }

        .permission-value-card input[type="radio"]:checked ~ .permission-value-check i {
            opacity: 1;
        }

        .permission-value-card:hover .permission-value-check {
            border-color: #3b82f6;
        }

        /* 步骤内容切换动画 */
        .step-content {
            transition: all 0.3s ease;
        }

        .step-content.hidden {
            opacity: 0;
            transform: translateX(-20px);
            pointer-events: none;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-6 py-6">

        <!-- 标签页导航 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-2 mb-6">
            <div class="flex">
                <button id="schoolsTab" class="tab-active flex-1 py-3 px-4 text-center font-medium text-sm rounded-xl transition-all focus:outline-none">
                    <i class="fas fa-school mr-2"></i>
                    学校管理
                    <span id="schoolsCount" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-semibold rounded-full badge-count-active">0</span>
                </button>
                <button id="publishersTab" class="tab-inactive flex-1 py-3 px-4 text-center font-medium text-sm rounded-xl transition-all focus:outline-none">
                    <i class="fas fa-building mr-2"></i>
                    供应商管理
                    <span id="publishersCount" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-semibold rounded-full badge-count">0</span>
                </button>
                <button id="dealersTab" class="tab-inactive flex-1 py-3 px-4 text-center font-medium text-sm rounded-xl transition-all focus:outline-none">
                    <i class="fas fa-store mr-2"></i>
                    经销商管理
                    <span id="dealersCount" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-semibold rounded-full badge-count">0</span>
                </button>
            </div>
        </div>

        <!-- 搜索和操作栏 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <!-- 左侧：搜索区域 -->
                <div class="flex items-center gap-3 flex-1">
                    <div class="relative flex-1 max-w-sm">
                        <input id="searchInput" type="text"
                               class="w-full h-10 pl-10 pr-4 py-2 bg-white border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                               placeholder="搜索组织名称...">
                        <div class="absolute left-3 top-2.5 text-slate-400">
                            <i class="fas fa-search text-sm"></i>
                        </div>
                    </div>
                    <button id="searchBtn" class="btn-primary h-10 px-4 text-white rounded-lg flex items-center gap-2 shadow-sm whitespace-nowrap text-sm">
                        <i class="fas fa-search text-xs"></i>
                        <span>搜索</span>
                    </button>
                    <button id="resetBtn" class="h-10 px-4 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors flex items-center gap-2 whitespace-nowrap text-sm">
                        <i class="fas fa-undo text-xs"></i>
                        <span>重置</span>
                    </button>
                </div>

                <!-- 右侧：排序和操作按钮 -->
                <div class="flex items-center gap-4">
                    <!-- 排序区域 -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-slate-700 whitespace-nowrap">排序:</span>
                        <select id="sortField" class="h-10 pl-3 pr-8 py-2 bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm min-w-[100px]">
                            <option value="user_count">用户数量</option>
                            <option value="name">名称</option>
                            <option value="id">ID</option>
                        </select>
                        <select id="sortOrder" class="h-10 pl-3 pr-8 py-2 bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm min-w-[70px]">
                            <option value="desc">降序</option>
                            <option value="asc">升序</option>
                        </select>
                    </div>

                    <!-- 操作按钮组 -->
                    <div class="flex gap-2">
                        <!-- 学校操作按钮 -->
                        <button id="addSchoolBtn" class="btn-success h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-plus text-xs"></i>
                            <span>添加学校</span>
                        </button>
                        <button id="importSchoolBtn" class="btn-purple h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-upload text-xs"></i>
                            <span>批量导入</span>
                        </button>

                        <!-- 出版社操作按钮 -->
                        <button id="addPublisherBtn" class="hidden btn-success h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-plus text-xs"></i>
                            <span>添加出版社</span>
                        </button>
                        <button id="importPublisherBtn" class="hidden btn-purple h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-upload text-xs"></i>
                            <span>批量导入</span>
                        </button>

                        <!-- 经销商操作按钮 -->
                        <button id="addDealerBtn" class="hidden btn-success h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-plus text-xs"></i>
                            <span>添加经销商</span>
                        </button>
                        <button id="importDealerBtn" class="hidden btn-purple h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-upload text-xs"></i>
                            <span>批量导入</span>
                        </button>

                        <!-- 批量权限设置按钮 -->
                        <button id="batchPermissionBtn" class="hidden btn-primary h-10 px-4 text-white rounded-lg flex items-center gap-2 text-sm whitespace-nowrap">
                            <i class="fas fa-cog text-xs"></i>
                            <span>批量设置权限</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学校列表区域 -->
        <div id="schoolsContainer" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200">
            <div class="p-8 flex justify-center items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>
        </div>

        <!-- 出版社列表区域 -->
        <div id="publishersContainer" class="hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200">
            <div class="p-8 flex justify-center items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>
        </div>

        <!-- 经销商列表区域 -->
        <div id="dealersContainer" class="hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200">
            <div class="p-8 flex justify-center items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-slate-600">加载中...</span>
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6">
            <!-- 信息显示区域 -->
            <div class="flex items-center">
                <p class="text-sm text-slate-700 mr-4">
                    第 <span id="currentPage" class="font-medium text-blue-600">1</span> 页，
                    共 <span id="totalPages" class="font-medium text-blue-600">1</span> 页，
                    共 <span id="totalCount" class="font-medium text-blue-600">0</span> 条
                </p>
            </div>

            <!-- 分页按钮区域 -->
            <div class="flex gap-1">
                <!-- 首页按钮 -->
                <button id="firstPageBtn" class="relative inline-flex items-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    <span class="sr-only">首页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- 上一页按钮 -->
                <button id="prevPageBtn" class="relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    上一页
                </button>

                <!-- 页码按钮容器 -->
                <div id="paginationNumbers" class="flex gap-1">
                    <!-- 页码将通过JavaScript动态生成 -->
                </div>

                <!-- 下一页按钮 -->
                <button id="nextPageBtn" class="relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    下一页
                </button>

                <!-- 末页按钮 -->
                <button id="lastPageBtn" class="relative inline-flex items-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    <span class="sr-only">末页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- 添加学校模态框 -->
        <div id="addSchoolModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800">添加学校</h3>
                        <button type="button" onclick="closeAddSchoolModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6">
                        <form id="addSchoolForm" class="space-y-4">
                            <div>
                                <label for="schoolName" class="block text-sm font-medium text-slate-700 mb-2">
                                    学校名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="schoolName" name="schoolName"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入学校名称">
                            </div>
                            <div>
                                <label for="schoolCity" class="block text-sm font-medium text-slate-700 mb-2">
                                    所在城市
                                </label>
                                <div class="custom-select" id="addSchoolCitySelectContainer">
                                    <div class="custom-select-trigger">
                                        <span class="custom-select-text">请选择城市</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索城市...">
                                        </div>
                                        <div class="custom-select-options">
                                            <!-- 城市选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="schoolCity" name="schoolCity">
                            </div>
                            <div>
                                <label for="schoolLevel" class="block text-sm font-medium text-slate-700 mb-2">
                                    办学层次
                                </label>
                                <select id="schoolLevel" name="schoolLevel"
                                        class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                    <option value="">请选择办学层次</option>
                                    <option value="中职">中职</option>
                                    <option value="专科">专科</option>
                                    <option value="本科">本科</option>
                                    <option value="技校">技校</option>
                                </select>
                            </div>
                        </form>
                    </div>

                    <!-- 模态框按钮 -->
                    <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                        <button type="button" onclick="closeAddSchoolModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            取消
                        </button>
                        <button type="button" id="saveSchoolBtn"
                                class="btn-primary px-6 py-3 text-white rounded-xl font-medium">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加出版社模态框 -->
        <div id="addPublisherModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800">添加出版社</h3>
                        <button type="button" onclick="closeAddPublisherModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6 overflow-y-auto max-h-[70vh]">
                        <form id="addPublisherForm" class="space-y-4">
                            <div>
                                <label for="publisherName" class="block text-sm font-medium text-slate-700 mb-2">
                                    出版社名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="publisherName" name="publisherName"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入出版社名称">
                            </div>

                            <div>
                                <label for="publisherAddress" class="block text-sm font-medium text-slate-700 mb-2">地址</label>
                                <input type="text" id="publisherAddress" name="publisherAddress"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入地址">
                            </div>

                            <div>
                                <label for="publisherPhone" class="block text-sm font-medium text-slate-700 mb-2">联系电话</label>
                                <input type="text" id="publisherPhone" name="publisherPhone"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入联系电话">
                            </div>

                            <div class="bg-slate-50 rounded-xl p-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="isPublisher" name="isPublisher"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded" checked>
                                    <label for="isPublisher" class="ml-3 block text-sm text-slate-700">仅为出版社</label>
                                </div>
                            </div>

                            <div class="border-t border-slate-200 pt-4">
                                <h4 class="text-sm font-medium text-slate-700 mb-3">权限设置</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="canRecommendBooks" name="canRecommendBooks"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="canRecommendBooks" class="ml-3 block text-sm text-slate-700">允许换版推荐</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="canRegisterExhibition" name="canRegisterExhibition"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="canRegisterExhibition" class="ml-3 block text-sm text-slate-700">允许书展报名</label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 模态框按钮 -->
                    <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                        <button type="button" onclick="closeAddPublisherModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            取消
                        </button>
                        <button type="button" id="savePublisherBtn"
                                class="btn-primary px-6 py-3 text-white rounded-xl font-medium">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加经销商模态框 -->
        <div id="addDealerModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800">添加经销商</h3>
                        <button type="button" onclick="closeAddDealerModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6 overflow-y-auto max-h-[70vh]">
                        <form id="addDealerForm" class="space-y-4">
                            <div>
                                <label for="dealerName" class="block text-sm font-medium text-slate-700 mb-2">
                                    经销商名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="dealerName" name="dealerName"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入经销商名称">
                            </div>

                            <div>
                                <label for="dealerAddress" class="block text-sm font-medium text-slate-700 mb-2">地址</label>
                                <input type="text" id="dealerAddress" name="dealerAddress"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入地址">
                            </div>

                            <div>
                                <label for="dealerPhone" class="block text-sm font-medium text-slate-700 mb-2">联系电话</label>
                                <input type="text" id="dealerPhone" name="dealerPhone"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入联系电话">
                            </div>

                            <div>
                                <label for="parentDealerId" class="block text-sm font-medium text-slate-700 mb-2">上级经销商</label>
                                <div class="custom-select" id="parentDealerContainer">
                                    <div class="custom-select-trigger">
                                        <span class="custom-select-text">请选择上级经销商</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索上级经销商...">
                                        </div>
                                        <div class="custom-select-options" id="parentDealerOptions">
                                            <!-- 选项将由JS动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="parentDealerId" name="parentDealerId">
                            </div>

                            <div class="border-t border-slate-200 pt-4">
                                <h4 class="text-sm font-medium text-slate-700 mb-3">权限设置</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="dealerCanRecommendBooks" name="dealerCanRecommendBooks"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="dealerCanRecommendBooks" class="ml-3 block text-sm text-slate-700">允许换版推荐</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="dealerCanInviteUsers" name="dealerCanInviteUsers"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="dealerCanInviteUsers" class="ml-3 block text-sm text-slate-700">允许邀请用户注册</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="dealerCanInitiateExhibition" name="dealerCanInitiateExhibition"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="dealerCanInitiateExhibition" class="ml-3 block text-sm text-slate-700">允许发起书展</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="dealerCanRegisterExhibition" name="dealerCanRegisterExhibition"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                        <label for="dealerCanRegisterExhibition" class="ml-3 block text-sm text-slate-700">允许书展报名</label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 模态框按钮 -->
                    <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                        <button type="button" onclick="closeAddDealerModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            取消
                        </button>
                        <button type="button" id="saveDealerBtn"
                                class="btn-primary px-6 py-3 text-white rounded-xl font-medium">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学校详情查看/编辑模态框 -->
        <div id="schoolDetailsModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-x-hidden">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800" id="schoolDetailsTitle">学校详情</h3>
                        <button type="button" onclick="closeSchoolDetailsModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6">
                        <form id="schoolDetailsForm" class="space-y-4">
                            <div>
                                <label for="schoolDetailsName" class="block text-sm font-medium text-slate-700 mb-2">
                                    学校名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="schoolDetailsName" name="schoolDetailsName"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入学校名称" readonly>
                            </div>
                            <div>
                                <label for="schoolDetailsCity" class="block text-sm font-medium text-slate-700 mb-2">
                                    所在城市
                                </label>
                                <div class="custom-select" id="citySelectContainer" style="display: none;">
                                    <div class="custom-select-trigger">
                                        <span class="custom-select-text">请选择城市</span>
                                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                                    </div>
                                    <div class="custom-select-dropdown">
                                        <div class="custom-select-search">
                                            <input type="text" placeholder="搜索城市...">
                                        </div>
                                        <div class="custom-select-options">
                                            <!-- 城市选项将动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <input type="text" id="schoolDetailsCity" name="schoolDetailsCity"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="请输入所在城市" readonly>
                            </div>
                            <div>
                                <label for="schoolDetailsLevel" class="block text-sm font-medium text-slate-700 mb-2">
                                    办学层次
                                </label>
                                <select id="schoolDetailsLevel" name="schoolDetailsLevel"
                                        class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        disabled>
                                    <option value="">请选择办学层次</option>
                                    <option value="中职">中职</option>
                                    <option value="专科">专科</option>
                                    <option value="本科">本科</option>
                                    <option value="技校">技校</option>
                                </select>
                            </div>
                        </form>
                    </div>

                    <!-- 模态框按钮 -->
                    <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                        <button type="button" onclick="closeSchoolDetailsModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            取消
                        </button>
                        <button type="button" id="editSchoolBtn"
                                class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium">
                            编辑
                        </button>
                        <button type="button" id="saveSchoolDetailsBtn" style="display: none;"
                                class="btn-primary px-6 py-3 text-white rounded-xl font-medium">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查看单位用户模态框 -->
        <div id="viewCompanyUsersModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] flex flex-col overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800">查看单位用户</h3>
                        <button type="button" onclick="closeViewCompanyUsersModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                    <div class="p-6 flex-1 overflow-auto">
                        <div id="companyUsersContainer" class="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h4 class="text-lg font-medium text-slate-800" id="companyUsersTitle">用户列表</h4>
                                <div class="text-sm text-slate-600" id="companyUsersCount">共 0 名用户</div>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-slate-200">
                                    <thead class="bg-slate-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">用户名</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">姓名</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">角色</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">手机号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">邮箱</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">注册时间</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="companyUsersTableBody" class="bg-white divide-y divide-slate-200">
                                        <!-- 用户列表将由JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-6 flex justify-between items-center">
                                <div class="text-sm text-slate-700">
                                    <span id="companyUsersPageInfo">显示 1-10 项，共 0 项</span>
                                </div>
                                <div class="flex gap-1">
                                    <button id="companyUsersPrevBtn" class="px-3 py-2 bg-white border border-slate-300 rounded-xl text-slate-700 hover:bg-slate-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button id="companyUsersNextBtn" class="px-3 py-2 bg-white border border-slate-300 rounded-xl text-slate-700 hover:bg-slate-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6 border-t border-slate-200 flex justify-end">
                        <button type="button" onclick="closeViewCompanyUsersModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
            </div>
        </div>
        
        <!-- 编辑权限模态框 -->
        <div id="editPermissionsModalContainer" class="fixed inset-0 z-50 hidden">
            <div class="modal-overlay flex items-center justify-center p-4">
                <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <h3 class="text-xl font-semibold text-slate-800" id="editPermissionsTitle">编辑权限</h3>
                        <button type="button" onclick="closeEditPermissionsModal()"
                                class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <form id="editPermissionsForm" class="space-y-4">
                            <input type="hidden" id="editCompanyId" name="editCompanyId">
                            <input type="hidden" id="editCompanyType" name="editCompanyType">

                            <div id="publisherPermissions" class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox" id="editPublisherCanRecommendBooks" name="editPublisherCanRecommendBooks"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editPublisherCanRecommendBooks" class="ml-3 block text-sm text-slate-700">允许换版推荐</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="editPublisherCanRegisterExhibition" name="editPublisherCanRegisterExhibition"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editPublisherCanRegisterExhibition" class="ml-3 block text-sm text-slate-700">允许书展报名</label>
                                </div>
                            </div>

                            <div id="dealerPermissions" class="space-y-3 hidden">
                                <div class="flex items-center">
                                    <input type="checkbox" id="editDealerCanRecommendBooks" name="editDealerCanRecommendBooks"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editDealerCanRecommendBooks" class="ml-3 block text-sm text-slate-700">允许换版推荐</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="editDealerCanInviteUsers" name="editDealerCanInviteUsers"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editDealerCanInviteUsers" class="ml-3 block text-sm text-slate-700">允许邀请用户注册</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="editDealerCanInitiateExhibition" name="editDealerCanInitiateExhibition"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editDealerCanInitiateExhibition" class="ml-3 block text-sm text-slate-700">允许发起书展</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="editDealerCanRegisterExhibition" name="editDealerCanRegisterExhibition"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                    <label for="editDealerCanRegisterExhibition" class="ml-3 block text-sm text-slate-700">允许书展报名</label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                        <button type="button" onclick="closeEditPermissionsModal()"
                                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-medium">
                            取消
                        </button>
                        <button type="button" id="editSavePermissionsBtn"
                                class="btn-primary px-6 py-3 text-white rounded-xl font-medium">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 提示模态框 -->
        <div id="messageModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800" id="messageModalTitle">提示</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#messageModalContainer').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <p id="messageModalContent" class="text-gray-700"></p>
                </div>
                <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg border-t border-gray-200">
                    <button type="button" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#messageModalContainer').addClass('hidden')">确定</button>
                </div>
            </div>
        </div>


        
        <!-- 出版社权限设置模态框 -->
        <div id="permissionsModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800" id="permissionsModalTitle">权限设置</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closePermissionsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="permissionRecommendBooks" name="permissionRecommendBooks" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="permissionRecommendBooks" class="ml-2 block text-sm text-gray-900">允许换版推荐</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="permissionRegisterExhibition" name="permissionRegisterExhibition" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="permissionRegisterExhibition" class="ml-2 block text-sm text-gray-900">允许书展报名</label>
                        </div>
                    </div>
                    <input type="hidden" id="publisherIdForPermissions" value="">
                </div>
                <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="closePermissionsModal()">取消</button>
                    <button type="button" id="publisherSavePermissionsBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">保存</button>
                </div>
            </div>
        </div>
        
        <!-- 经销商权限设置模态框 -->
        <div id="dealerPermissionsModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800" id="dealerPermissionsModalTitle">经销商权限设置</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#dealerPermissionsModal').addClass('hidden')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="dealerPermissionRecommendBooks" name="dealerPermissionRecommendBooks" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="dealerPermissionRecommendBooks" class="ml-2 block text-sm text-gray-900">允许换版推荐</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="dealerPermissionInviteUsers" name="dealerPermissionInviteUsers" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="dealerPermissionInviteUsers" class="ml-2 block text-sm text-gray-900">允许邀请用户</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="dealerPermissionInitiateExhibition" name="dealerPermissionInitiateExhibition" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="dealerPermissionInitiateExhibition" class="ml-2 block text-sm text-gray-900">允许发起书展</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="dealerPermissionRegisterExhibition" name="dealerPermissionRegisterExhibition" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="dealerPermissionRegisterExhibition" class="ml-2 block text-sm text-gray-900">允许书展报名</label>
                        </div>
                    </div>
                </div>
                <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#dealerPermissionsModal').addClass('hidden')">取消</button>
                    <button type="button" id="saveDealerPermissionsBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">保存</button>
                </div>
            </div>
        </div>

        <!-- 确认删除模态框 -->
        <div id="confirmDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800" id="confirmDeleteModalTitle">确认删除</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#confirmDeleteModal').addClass('hidden')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <p id="confirmDeleteModalContent" class="text-gray-700 mb-2">您确定要删除该组织吗？该操作不可逆！</p>
            <p class="text-red-600 text-sm" id="confirmDeleteModalWarning">删除前请确保该组织下没有关联用户或其他数据，否则将无法删除。</p>
            <input type="hidden" id="deleteItemId" value="">
            <input type="hidden" id="deleteItemType" value="">
        </div>
        <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg border-t border-gray-200 flex justify-end space-x-3">
            <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#confirmDeleteModal').addClass('hidden')">取消</button>
            <button type="button" id="confirmDeleteBtn" class="px-5 py-2.5 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 transition-colors text-base font-medium">确认删除</button>
        </div>
    </div>
</div>

<!-- 批量设置权限模态框 -->
<div id="batchPermissionModal" class="fixed inset-0 z-50 hidden">
    <div class="modal-overlay flex items-center justify-center p-4">
        <!-- 调整尺寸的模态框容器 - 更窄更高 -->
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl overflow-hidden flex flex-col" style="height: 750px;">
            <!-- 模态框头部 - 固定高度 -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0" style="height: 80px;">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-cog text-blue-600 text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-slate-800">批量设置权限</h3>
                        <p class="text-xs text-slate-500">为多个组织统一配置权限设置</p>
                    </div>
                </div>
                <button type="button" onclick="closeBatchPermissionModal()"
                        class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            <!-- 模态框内容 - 剩余高度，可滚动 -->
            <div class="flex-1 overflow-hidden" style="height: 670px;">
                <div class="flex h-full">
                    <!-- 左侧操作面板 -->
                    <div class="w-72 flex-shrink-0 modal-left-panel border-r border-slate-200" style="height: 670px;">
                        <div class="flex flex-col h-full p-6">
                            <!-- 进度指示器 -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between text-xs text-slate-500 mb-2">
                                    <span>操作步骤</span>
                                    <span id="stepIndicator">1/3</span>
                                </div>
                                <div class="w-full bg-slate-200 rounded-full h-2">
                                    <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 33.33%"></div>
                                </div>
                            </div>

                            <!-- 步骤1：权限类型选择 -->
                            <div id="step1" class="step-content">
                                <h4 class="text-sm font-semibold text-slate-800 mb-4 flex items-center">
                                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mr-2">1</span>
                                    选择权限类型
                                </h4>

                                <!-- 出版社权限 -->
                                <div id="publisherPermissionTypes" class="mb-4">
                                    <h5 class="text-xs font-medium text-slate-600 mb-3 uppercase tracking-wide">出版社权限</h5>
                                    <div class="space-y-2">
                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_publisher_recommend" name="permission_type" value="publisher_can_recommend_books" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-green-100 text-green-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-sync-alt text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">换版推荐</div>
                                                <div class="text-xs text-slate-500">允许推荐新版本图书</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>

                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_publisher_exhibition" name="permission_type" value="publisher_can_register_exhibition" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-purple-100 text-purple-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-calendar-check text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">书展报名</div>
                                                <div class="text-xs text-slate-500">参与书展活动报名</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- 经销商权限 -->
                                <div id="dealerPermissionTypes" class="mb-4">
                                    <h5 class="text-xs font-medium text-slate-600 mb-3 uppercase tracking-wide">经销商权限</h5>
                                    <div class="space-y-2">
                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_dealer_recommend" name="permission_type" value="dealer_can_recommend_books" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-green-100 text-green-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-sync-alt text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">换版推荐</div>
                                                <div class="text-xs text-slate-500">允许推荐新版本图书</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>

                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_dealer_invite" name="permission_type" value="dealer_can_invite_users" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-user-plus text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">邀请用户</div>
                                                <div class="text-xs text-slate-500">邀请新用户注册</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>

                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_dealer_initiate" name="permission_type" value="dealer_can_initiate_exhibition" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-plus-circle text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">发起书展</div>
                                                <div class="text-xs text-slate-500">创建新的书展活动</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>

                                        <label class="permission-card flex items-center p-3 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                                            <input type="radio" id="perm_type_dealer_exhibition" name="permission_type" value="dealer_can_register_exhibition" class="sr-only">
                                            <div class="permission-card-icon w-8 h-8 bg-purple-100 text-purple-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-calendar-check text-sm"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-slate-800">书展报名</div>
                                                <div class="text-xs text-slate-500">参与书展活动报名</div>
                                            </div>
                                            <div class="permission-card-check w-5 h-5 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs opacity-0"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 步骤2：权限值设置 -->
                            <div id="step2" class="step-content hidden">
                                <h4 class="text-sm font-semibold text-slate-800 mb-4 flex items-center">
                                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mr-2">2</span>
                                    设置权限值
                                </h4>

                                <div class="space-y-3">
                                    <label class="permission-value-card flex items-center p-4 border border-slate-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all cursor-pointer">
                                        <input type="radio" id="permission_value_true" name="permission_value" value="true" class="sr-only" checked>
                                        <div class="permission-value-icon w-12 h-12 bg-green-100 text-green-600 rounded-xl flex items-center justify-center mr-4">
                                            <i class="fas fa-check text-xl"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-base font-semibold text-slate-800">允许</div>
                                            <div class="text-sm text-slate-500">启用此权限功能</div>
                                        </div>
                                        <div class="permission-value-check w-6 h-6 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-white text-sm opacity-0"></i>
                                        </div>
                                    </label>

                                    <label class="permission-value-card flex items-center p-4 border border-slate-200 rounded-xl hover:border-red-300 hover:bg-red-50 transition-all cursor-pointer">
                                        <input type="radio" id="permission_value_false" name="permission_value" value="false" class="sr-only">
                                        <div class="permission-value-icon w-12 h-12 bg-red-100 text-red-600 rounded-xl flex items-center justify-center mr-4">
                                            <i class="fas fa-times text-xl"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-base font-semibold text-slate-800">禁止</div>
                                            <div class="text-sm text-slate-500">禁用此权限功能</div>
                                        </div>
                                        <div class="permission-value-check w-6 h-6 border-2 border-slate-300 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-white text-sm opacity-0"></i>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 步骤3：选择单位 -->
                            <div id="step3" class="step-content hidden">
                                <h4 class="text-sm font-semibold text-slate-800 mb-4 flex items-center">
                                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mr-2">3</span>
                                    选择目标单位
                                </h4>

                                <!-- 选择统计 -->
                                <div class="bg-slate-50 rounded-xl p-4 mb-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600" id="selectedCount">0</div>
                                        <div class="text-sm text-slate-600">已选择单位</div>
                                    </div>
                                </div>

                                <!-- 操作提示 -->
                                <div class="text-xs text-slate-500 space-y-1">
                                    <p>• 在右侧列表中选择要设置权限的单位</p>
                                    <p>• 可以使用筛选和排序功能快速定位</p>
                                    <p>• 支持批量选择和搜索功能</p>
                                </div>
                            </div>

                            <!-- 导航按钮 -->
                            <div class="mt-auto pt-4 border-t border-slate-200">
                                <div class="flex justify-between">
                                    <button type="button" id="prevStepBtn" class="px-4 py-2 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        <i class="fas fa-chevron-left mr-1"></i>上一步
                                    </button>
                                    <button type="button" id="nextStepBtn" class="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                                        下一步<i class="fas fa-chevron-right ml-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧预览面板 -->
                    <div class="flex-1 modal-content-area" style="height: 670px;">
                        <div class="h-full overflow-y-auto custom-scrollbar p-6">
                            <!-- 操作预览 -->
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                    <i class="fas fa-eye text-blue-600 mr-2"></i>
                                    操作预览
                                </h4>

                                <!-- 预览卡片 -->
                                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                                    <div class="space-y-4">
                                        <!-- 权限类型预览 -->
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-cog text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-slate-700">权限类型</div>
                                                <div id="previewPermissionType" class="text-lg font-semibold text-slate-800">请选择权限类型</div>
                                            </div>
                                        </div>

                                        <!-- 权限值预览 -->
                                        <div class="flex items-center">
                                            <div id="previewValueIcon" class="w-8 h-8 bg-green-100 text-green-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-check text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-slate-700">权限设置</div>
                                                <div id="previewPermissionValue" class="text-lg font-semibold text-slate-800">允许</div>
                                            </div>
                                        </div>

                                        <!-- 目标单位预览 -->
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-building text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-slate-700">目标单位</div>
                                                <div id="previewTargetCount" class="text-lg font-semibold text-slate-800">未选择</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 数据统计 -->
                                <div id="dataStatsSection" class="hidden mt-6">
                                    <h5 class="text-sm font-semibold text-slate-800 mb-3 flex items-center">
                                        <i class="fas fa-chart-bar text-blue-600 mr-2"></i>
                                        数据统计
                                    </h5>
                                    <div class="bg-slate-50 rounded-xl p-4">
                                        <div class="grid grid-cols-2 gap-4 text-center">
                                            <div>
                                                <div class="text-2xl font-bold text-slate-800" id="totalCompaniesCount">0</div>
                                                <div class="text-xs text-slate-500">总计单位</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold text-green-600" id="enabledCompaniesCount">0</div>
                                                <div class="text-xs text-slate-500">已启用</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold text-red-600" id="disabledCompaniesCount">0</div>
                                                <div class="text-xs text-slate-500">已禁用</div>
                                            </div>
                                            <div>
                                                <div class="text-2xl font-bold text-blue-600" id="filteredCompaniesCount">0</div>
                                                <div class="text-xs text-slate-500">筛选结果</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 单位列表 -->
                            <div id="companiesListSection" class="hidden">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-semibold text-slate-800 flex items-center">
                                        <i class="fas fa-list text-blue-600 mr-2"></i>
                                        单位列表
                                    </h4>
                                    <button type="button" id="refreshCompaniesBtn" class="px-3 py-1.5 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors text-sm">
                                        <i class="fas fa-sync-alt mr-1"></i>刷新
                                    </button>
                                </div>

                                <!-- 筛选和搜索工具栏 -->
                                <div class="bg-slate-50 rounded-xl p-4 mb-4 space-y-3">
                                    <!-- 搜索框 -->
                                    <div class="relative">
                                        <input type="text" id="companySearchInput"
                                               class="w-full h-10 pl-10 pr-4 py-2 bg-white border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                                               placeholder="搜索单位名称...">
                                        <div class="absolute left-3 top-2.5 text-slate-400">
                                            <i class="fas fa-search text-sm"></i>
                                        </div>
                                    </div>

                                    <!-- 筛选和排序 -->
                                    <div class="flex flex-wrap gap-3">
                                        <!-- 状态筛选 -->
                                        <div class="flex-1 min-w-0">
                                            <label class="block text-xs font-medium text-slate-600 mb-1">状态筛选</label>
                                            <select id="statusFilter" class="w-full h-8 px-2 py-1 bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs">
                                                <option value="all">全部</option>
                                                <option value="enabled">已启用</option>
                                                <option value="disabled">已禁用</option>
                                            </select>
                                        </div>

                                        <!-- 排序方式 -->
                                        <div class="flex-1 min-w-0">
                                            <label class="block text-xs font-medium text-slate-600 mb-1">排序方式</label>
                                            <select id="sortBy" class="w-full h-8 px-2 py-1 bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs">
                                                <option value="name">按名称</option>
                                                <option value="status">按状态</option>
                                            </select>
                                        </div>

                                        <!-- 排序顺序 -->
                                        <div class="flex-1 min-w-0">
                                            <label class="block text-xs font-medium text-slate-600 mb-1">排序顺序</label>
                                            <select id="sortOrder" class="w-full h-8 px-2 py-1 bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs">
                                                <option value="asc">升序</option>
                                                <option value="desc">降序</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- 全选和统计 -->
                                    <div class="flex items-center justify-between pt-2 border-t border-slate-200">
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="selectAllCompanies" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded">
                                            <span class="ml-2 text-sm font-medium text-slate-700">全选当前筛选结果</span>
                                        </label>
                                        <div class="text-xs text-slate-500">
                                            <span id="filteredCount">0</span> 个单位 | 已选择 <span id="selectedCountText">0</span> 个
                                        </div>
                                    </div>
                                </div>

                                <!-- 单位列表容器 -->
                                <div id="companiesContainer" class="border border-slate-200 rounded-xl overflow-hidden">
                                    <div class="flex justify-center items-center h-32">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                        <span class="ml-2 text-slate-600">加载中...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作说明 -->
                            <div class="mt-6 p-4 bg-slate-50 rounded-xl">
                                <h5 class="text-sm font-medium text-slate-700 mb-2 flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                    操作说明
                                </h5>
                                <div class="text-xs text-slate-600 space-y-1">
                                    <p>• 第一步：选择要设置的权限类型</p>
                                    <p>• 第二步：选择权限值（允许或禁止）</p>
                                    <p>• 第三步：选择要应用的目标单位</p>
                                    <p>• 完成后点击保存按钮批量应用设置</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <!-- 批量导入学校模态框 -->
    <div id="importSchoolModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">批量导入学校</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#importSchoolModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 bg-gray-50">
                <div class="mb-4">
                    <p class="text-gray-700 mb-2">请先下载导入模板，按照模板格式填写学校信息后上传文件。</p>
                    <a href="/api/admin/download_organization_import_template?type=school" class="text-blue-600 hover:text-blue-800 inline-flex items-center">
                        <i class="fas fa-download mr-1"></i> 下载导入模板
                    </a>
                </div>
                <form id="importSchoolForm" class="space-y-5 bg-white p-5 rounded-md shadow-sm">
                    <div class="form-group">
                        <label for="schoolImportFile" class="block text-sm font-medium text-gray-700 mb-1">选择Excel文件 <span class="text-red-500">*</span></label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="fas fa-file-excel text-gray-400 text-3xl mb-2"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="schoolImportFile" class="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>选择文件</span>
                                        <input id="schoolImportFile" name="file" type="file" class="sr-only" accept=".xlsx, .xls">
                                    </label>
                                    <p class="pl-1">或拖放文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">支持XLSX、XLS格式</p>
                            </div>
                        </div>
                        <div id="schoolImportFileInfo" class="mt-2 text-sm text-gray-500 hidden">
                            已选择文件: <span id="schoolImportFileName"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-3 border-t border-gray-200">
                <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#importSchoolModalContainer').addClass('hidden')">取消</button>
                <button type="button" id="importSchoolsBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">导入</button>
            </div>
        </div>
    </div>

    <!-- 批量导入出版社模态框 -->
    <div id="importPublisherModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">批量导入出版社</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#importPublisherModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 bg-gray-50">
                <div class="mb-4">
                    <p class="text-gray-700 mb-2">请先下载导入模板，按照模板格式填写出版社信息后上传文件。</p>
                    <a href="/api/admin/download_organization_import_template?type=publisher" class="text-blue-600 hover:text-blue-800 inline-flex items-center">
                        <i class="fas fa-download mr-1"></i> 下载导入模板
                    </a>
                </div>
                <form id="importPublisherForm" class="space-y-5 bg-white p-5 rounded-md shadow-sm">
                    <div class="form-group">
                        <label for="publisherImportFile" class="block text-sm font-medium text-gray-700 mb-1">选择Excel文件 <span class="text-red-500">*</span></label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="fas fa-file-excel text-gray-400 text-3xl mb-2"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="publisherImportFile" class="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>选择文件</span>
                                        <input id="publisherImportFile" name="file" type="file" class="sr-only" accept=".xlsx, .xls">
                                    </label>
                                    <p class="pl-1">或拖放文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">支持XLSX、XLS格式</p>
                            </div>
                        </div>
                        <div id="publisherImportFileInfo" class="mt-2 text-sm text-gray-500 hidden">
                            已选择文件: <span id="publisherImportFileName"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-3 border-t border-gray-200">
                <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#importPublisherModalContainer').addClass('hidden')">取消</button>
                <button type="button" id="importPublishersBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">导入</button>
            </div>
        </div>
    </div>

    <!-- 批量导入经销商模态框 -->
    <div id="importDealerModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">批量导入经销商</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#importDealerModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 bg-gray-50">
                <div class="mb-4">
                    <p class="text-gray-700 mb-2">请先下载导入模板，按照模板格式填写经销商信息后上传文件。</p>
                    <a href="/api/admin/download_organization_import_template?type=dealer" class="text-blue-600 hover:text-blue-800 inline-flex items-center">
                        <i class="fas fa-download mr-1"></i> 下载导入模板
                    </a>
                </div>
                <form id="importDealerForm" class="space-y-5 bg-white p-5 rounded-md shadow-sm">
                    <div class="form-group">
                        <label for="dealerImportFile" class="block text-sm font-medium text-gray-700 mb-1">选择Excel文件 <span class="text-red-500">*</span></label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="fas fa-file-excel text-gray-400 text-3xl mb-2"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="dealerImportFile" class="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>选择文件</span>
                                        <input id="dealerImportFile" name="file" type="file" class="sr-only" accept=".xlsx, .xls">
                                    </label>
                                    <p class="pl-1">或拖放文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">支持XLSX、XLS格式</p>
                            </div>
                        </div>
                        <div id="dealerImportFileInfo" class="mt-2 text-sm text-gray-500 hidden">
                            已选择文件: <span id="dealerImportFileName"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-3 border-t border-gray-200">
                <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#importDealerModalContainer').addClass('hidden')">取消</button>
                <button type="button" id="importDealersBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">导入</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let currentTab = 'schools'; // schools, publishers, dealers
        let messageId = 0;
        let currentSchoolId = null;
        let citySelect = null;
        let addressData = null;
        let citySelectInitialized = false;
        let addSchoolCitySelect = null;
        let addSchoolCitySelectInitialized = false;

        // 批量权限设置相关变量
        let currentStep = 1;
        let selectedPermissionType = '';
        let selectedPermissionValue = 'true';
        let selectedCompanies = [];
        let allCompanies = []; // 存储所有单位数据
        let filteredCompanies = []; // 存储筛选后的单位数据

        // 消息提示函数
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-xl shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }
        }

        // 模态框关闭函数
        function closeAddSchoolModal() {
            console.log('关闭添加学校模态框');

            // 清理添加学校城市选择器
            destroyAddSchoolCitySelector();

            $('#addSchoolModalContainer').addClass('hidden');
        }

        function closeAddPublisherModal() {
            $('#addPublisherModalContainer').addClass('hidden');
        }

        function closeAddDealerModal() {
            $('#addDealerModalContainer').addClass('hidden');
        }

        function closeViewCompanyUsersModal() {
            $('#viewCompanyUsersModalContainer').addClass('hidden');
        }

        function closeSchoolDetailsModal() {
            console.log('关闭学校详情模态框');

            // 彻底清理城市选择器
            destroyCitySelector();

            // 重置表单状态
            setSchoolDetailsReadonly(true);

            // 隐藏模态框
            $('#schoolDetailsModalContainer').addClass('hidden');
        }

        function closeEditPermissionsModal() {
            $('#editPermissionsModalContainer').addClass('hidden');
        }

        // 自定义搜索下拉框类
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.trigger = this.container.find('.custom-select-trigger');
                this.dropdown = this.container.find('.custom-select-dropdown');
                this.searchInput = this.container.find('.custom-select-search input');
                this.optionsContainer = this.container.find('.custom-select-options');
                this.textSpan = this.trigger.find('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.disabled = options.disabled || false;
                this.onSelect = options.onSelect || null;

                this.init();
            }

            init() {
                // 绑定事件
                this.trigger.on('click', (e) => {
                    if (!this.disabled) {
                        this.toggle();
                    }
                });

                // 搜索功能
                this.searchInput.on('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击选项
                this.optionsContainer.on('click', '.custom-select-option:not(.no-results)', (e) => {
                    const option = $(e.target);
                    const value = option.data('value');
                    const text = option.text();
                    this.selectOption(value, text);
                });

                // 点击外部关闭
                $(document).on('click', (e) => {
                    if (!this.container.is(e.target) && this.container.has(e.target).length === 0) {
                        this.close();
                    }
                });
            }

            toggle() {
                if (this.container.hasClass('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.addClass('active');
                this.searchInput.focus();
            }

            close() {
                this.container.removeClass('active');
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            renderOptions() {
                this.optionsContainer.empty();

                if (this.options.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">暂无选项</div>');
                    return;
                }

                this.options.forEach(option => {
                    const isSelected = option.value === this.selectedValue;
                    const optionEl = $(`<div class="custom-select-option ${isSelected ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`);
                    this.optionsContainer.append(optionEl);
                });
            }

            filterOptions(query) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(query.toLowerCase())
                );

                this.optionsContainer.empty();

                if (filteredOptions.length === 0) {
                    this.optionsContainer.append('<div class="custom-select-option no-results">未找到匹配项</div>');
                    return;
                }

                filteredOptions.forEach(option => {
                    const isSelected = option.value === this.selectedValue;
                    const optionEl = $(`<div class="custom-select-option ${isSelected ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`);
                    this.optionsContainer.append(optionEl);
                });
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.text(text);
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            getText() {
                return this.selectedText;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.text(this.placeholder);
                this.searchInput.val('');
                this.renderOptions();
                this.close();
            }
        }
        let allSchools = [];
        let allPublishers = [];
        let allDealers = [];
        let companyUsersPage = 1;
        let companyUsersPageSize = 10;
        let currentCompanyId = null;
        let currentCompanyType = null;
        
        $(document).ready(function() {
            // 初始化页面
            initTabs();
            loadSchools();
            loadCounters(); // 立即加载计数器
            loadAddressData(); // 加载地址数据
            initParentDealerSearch(); // 初始化上级经销商搜索
            initFileUploadHandlers(); // 初始化文件上传处理器
            
            // 绑定搜索事件
            $('#searchBtn').click(function() {
                currentPage = 1;
                if (currentTab === 'schools') {
                    loadSchools();
                } else if (currentTab === 'publishers') {
                    loadPublishers();
                } else if (currentTab === 'dealers') {
                    loadDealers();
                }
            });

            // 绑定重置按钮事件
            $('#resetBtn').click(function() {
                // 重置搜索输入框
                $('#searchInput').val('');
                // 重置排序选项
                $('#sortField').val('user_count');
                $('#sortOrder').val('desc');
                // 重置页码
                currentPage = 1;
                // 重新加载当前标签页数据
                if (currentTab === 'schools') {
                    loadSchools();
                } else if (currentTab === 'publishers') {
                    loadPublishers();
                } else if (currentTab === 'dealers') {
                    loadDealers();
                }
            });
            
            // 绑定排序字段和排序方式改变事件
            $('#sortField, #sortOrder').change(function() {
                currentPage = 1;
                if (currentTab === 'schools') {
                    loadSchools();
                } else if (currentTab === 'publishers') {
                    loadPublishers();
                } else if (currentTab === 'dealers') {
                    loadDealers();
                }
            });
            
            // 绑定回车搜索
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                    return false;
                }
            });
            
            // 绑定分页事件
            $('#firstPageBtn').click(function() {
                if (currentPage > 1) {
                    goToPage(1);
                }
            });
            
            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    goToPage(currentPage - 1);
                }
            });
            
            $('#nextPageBtn').click(function() {
                if (currentPage < totalPages) {
                    goToPage(currentPage + 1);
                }
            });
            
            $('#lastPageBtn').click(function() {
                if (currentPage < totalPages) {
                    goToPage(totalPages);
                }
            });
            
            // 绑定公司用户分页事件
            $('#companyUsersPrevBtn').click(function() {
                if (companyUsersPage > 1) {
                    companyUsersPage--;
                    loadCompanyUsers(currentCompanyId, currentCompanyType);
                }
            });
            
            $('#companyUsersNextBtn').click(function() {
                if (companyUsersPage < Math.ceil($('#companyUsersContainer').data('total') / companyUsersPageSize)) {
                    companyUsersPage++;
                    loadCompanyUsers(currentCompanyId, currentCompanyType);
                }
            });
            
            // 绑定添加学校按钮事件
            $('#addSchoolBtn').click(function() {
                $('#schoolName').val('');
                $('#schoolCity').val('');
                $('#schoolLevel').val('');
                $('#addSchoolModalContainer').removeClass('hidden');

                // 延迟初始化城市选择器，确保模态框完全显示
                setTimeout(() => {
                    initAddSchoolCitySelector();
                }, 100);
            });
            
            // 绑定添加出版社按钮事件
            $('#addPublisherBtn').click(function() {
                $('#publisherName').val('');
                $('#publisherAddress').val('');
                $('#publisherPhone').val('');
                $('#isPublisher').prop('checked', true);
                $('#canRecommendBooks').prop('checked', false);
                $('#canRegisterExhibition').prop('checked', false);
                $('#addPublisherModalContainer').removeClass('hidden');
            });
            
            // 绑定添加经销商按钮事件
            $('#addDealerBtn').click(function() {
                $('#dealerName').val('');
                $('#dealerAddress').val('');
                $('#dealerPhone').val('');
                $('#parentDealerSearch').val('');
                $('#parentDealerId').val('');
                $('#dealerCanRecommendBooks').prop('checked', false);
                $('#dealerCanInviteUsers').prop('checked', false);
                $('#dealerCanInitiateExhibition').prop('checked', false);
                $('#dealerCanRegisterExhibition').prop('checked', false);
                initParentDealerDropdown();
                $('#addDealerModalContainer').removeClass('hidden');
            });
            
            // 绑定保存学校按钮事件
            $('#saveSchoolBtn').click(function() {
                saveSchool();
            });

            // 绑定编辑学校按钮事件
            $('#editSchoolBtn').click(function() {
                setSchoolDetailsReadonly(false);
            });

            // 绑定保存学校详情按钮事件
            $('#saveSchoolDetailsBtn').click(function() {
                saveSchoolDetails();
            });
            
            // 绑定保存出版社按钮事件
            $('#savePublisherBtn').click(function() {
                savePublisher();
            });
            
            // 绑定保存经销商按钮事件
            $('#saveDealerBtn').click(function() {
                saveDealer();
            });
            
            // 绑定保存出版社权限按钮事件
            $('#publisherSavePermissionsBtn').click(function() {
                savePublisherPermissions();
            });
            
            // 绑定保存经销商权限按钮事件
            $('#saveDealerPermissionsBtn').off('click').on('click', function() {
                // 从data属性中获取dealerId
                const dealerId = $(this).data('dealer-id');
                if (dealerId) {
                    saveDealerPermissions(dealerId);
                } else {
                    showMessage('错误', '无法获取经销商ID');
                }
            });
            
            // 绑定删除确认按钮事件
            $('#confirmDeleteBtn').off('click').on('click', function() {
                const itemId = $('#deleteItemId').val();
                const itemType = $('#deleteItemType').val();
                
                if (itemType === 'school') {
                    deleteSchool(itemId);
                } else if (itemType === 'publisher') {
                    deletePublisher(itemId);
                } else if (itemType === 'dealer') {
                    deleteDealer(itemId);
                } else if (itemType === 'user') {
                    deleteUser(itemId);
                }
            });
            
            // 初始化批量设置权限按钮
            $('#batchPermissionBtn').click(function() {
                openBatchPermissionModal();
            });
            

            
            // 绑定导入按钮事件
            $('#importSchoolBtn').click(function() {
                $('#schoolImportFile').val('');
                $('#schoolImportFileInfo').addClass('hidden');
                $('#importSchoolModalContainer').removeClass('hidden');
            });
            
            $('#importPublisherBtn').click(function() {
                $('#publisherImportFile').val('');
                $('#publisherImportFileInfo').addClass('hidden');
                $('#importPublisherModalContainer').removeClass('hidden');
            });
            
            $('#importDealerBtn').click(function() {
                $('#dealerImportFile').val('');
                $('#dealerImportFileInfo').addClass('hidden');
                $('#importDealerModalContainer').removeClass('hidden');
            });
            
            // 绑定导入提交按钮事件
            $('#importSchoolsBtn').click(function() {
                importOrganizations('school');
            });
            
            $('#importPublishersBtn').click(function() {
                importOrganizations('publisher');
            });
            
            $('#importDealersBtn').click(function() {
                importOrganizations('dealer');
            });
        });
        
        // 初始化选项卡
        function initTabs() {
            // 重置所有标签页为非激活状态
            $('#schoolsTab, #publishersTab, #dealersTab').removeClass('tab-active').addClass('tab-inactive');
            $('#schoolsTab').removeClass('tab-inactive').addClass('tab-active');

            // 重置所有徽章为非激活状态
            $('.badge-count-active').removeClass('badge-count-active').addClass('badge-count');
            $('#schoolsCount').removeClass('badge-count').addClass('badge-count-active');

            // 绑定选项卡点击事件
            $('#schoolsTab').click(function() {
                // 重置所有标签页
                $('#schoolsTab, #publishersTab, #dealersTab').removeClass('tab-active').addClass('tab-inactive');
                $(this).removeClass('tab-inactive').addClass('tab-active');

                // 重置所有徽章
                $('.badge-count-active').removeClass('badge-count-active').addClass('badge-count');
                $('#schoolsCount').removeClass('badge-count').addClass('badge-count-active');

                currentTab = 'schools';
                currentPage = 1;

                // 显示相应的内容区域
                $('#schoolsContainer').removeClass('hidden');
                $('#publishersContainer, #dealersContainer').addClass('hidden');

                // 显示相应的按钮
                $('#addSchoolBtn, #importSchoolBtn').removeClass('hidden');
                $('#addPublisherBtn, #importPublisherBtn, #addDealerBtn, #importDealerBtn, #batchPermissionBtn').addClass('hidden');

                // 加载数据
                loadSchools();
            });
            
            $('#publishersTab').click(function() {
                // 重置所有标签页
                $('#schoolsTab, #publishersTab, #dealersTab').removeClass('tab-active').addClass('tab-inactive');
                $(this).removeClass('tab-inactive').addClass('tab-active');

                // 重置所有徽章
                $('.badge-count-active').removeClass('badge-count-active').addClass('badge-count');
                $('#publishersCount').removeClass('badge-count').addClass('badge-count-active');

                currentTab = 'publishers';
                currentPage = 1;

                // 显示相应的内容区域
                $('#publishersContainer').removeClass('hidden');
                $('#schoolsContainer, #dealersContainer').addClass('hidden');

                // 显示相应的按钮
                $('#addPublisherBtn, #importPublisherBtn, #batchPermissionBtn').removeClass('hidden');
                $('#addSchoolBtn, #importSchoolBtn, #addDealerBtn, #importDealerBtn').addClass('hidden');

                // 加载数据
                loadPublishers();
            });

            $('#dealersTab').click(function() {
                // 重置所有标签页
                $('#schoolsTab, #publishersTab, #dealersTab').removeClass('tab-active').addClass('tab-inactive');
                $(this).removeClass('tab-inactive').addClass('tab-active');

                // 重置所有徽章
                $('.badge-count-active').removeClass('badge-count-active').addClass('badge-count');
                $('#dealersCount').removeClass('badge-count').addClass('badge-count-active');

                currentTab = 'dealers';
                currentPage = 1;

                // 显示相应的内容区域
                $('#dealersContainer').removeClass('hidden');
                $('#schoolsContainer, #publishersContainer').addClass('hidden');

                // 显示相应的按钮
                $('#addDealerBtn, #importDealerBtn, #batchPermissionBtn').removeClass('hidden');
                $('#addSchoolBtn, #importSchoolBtn, #addPublisherBtn, #importPublisherBtn').addClass('hidden');

                // 加载数据
                loadDealers();
            });
        }
        
        // 加载学校列表
        function loadSchools() {
            const keyword = $('#searchInput').val();
            const sortField = $('#sortField').val();
            const sortOrder = $('#sortOrder').val();
            
            // 显示加载状态
            $('#schoolsContainer').html(`
                <div class="p-12 flex flex-col items-center justify-center">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mb-4"></div>
                    <p class="text-slate-600 text-sm">正在加载学校数据...</p>
                </div>
            `);
            
            $.ajax({
                url: '/api/admin/get_all_schools',
                type: 'GET',
                data: {
                    page: currentPage,
                    page_size: pageSize,
                    keyword: keyword,
                    sort_field: sortField,
                    sort_order: sortOrder
                },
                success: function(response) {
                    if (response.code === 0) {
                        const { total, list, page, page_size, total_pages } = response.data;
                        
                        // 更新计数器
                        $('#schoolsCount').text(total);
                        
                        // 更新分页信息
                        currentPage = page;
                        updatePagination(total);
                        
                        // 更新数据列表
                        renderSchoolsList(list);
                        
                        // 缓存学校数据
                        allSchools = list;
                    } else {
                        showMessage(response.message || '获取学校列表失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }
        
        // 渲染学校列表
        function renderSchoolsList(schools) {
            if (!schools || schools.length === 0) {
                $('#schoolsContainer').html(`
                    <div class="p-12 text-center">
                        <div class="text-slate-400 mb-4">
                            <i class="fas fa-school text-4xl"></i>
                        </div>
                        <p class="text-slate-500 text-lg">没有找到学校数据</p>
                        <p class="text-slate-400 text-sm mt-2">请尝试调整搜索条件或添加新的学校</p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="overflow-hidden">
                    <table class="min-w-full divide-y divide-slate-200">
                        <thead class="bg-slate-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">学校名称</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">所在城市</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">办学层次</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">用户数量</th>
                                <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100">
            `;

            schools.forEach(school => {
                html += `
                    <tr class="hover:bg-slate-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-school text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900">${school.name}</div>
                                    <div class="text-xs text-slate-500">#${school.id}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="text-sm text-slate-900">${school.city || '未填写'}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="text-sm ${school.school_level ? 'text-slate-900' : 'text-slate-500'}">${school.school_level || '未设置'}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${school.user_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${school.user_count || 0} 人
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end gap-2">
                                <button class="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors text-xs"
                                        onclick="viewSchoolDetails(${school.id}, '${school.name}', '${school.city || ''}', '${school.school_level || ''}')">
                                    <i class="fas fa-eye mr-1.5"></i>
                                    查看
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-xs"
                                        onclick="viewCompanyUsers(${school.id}, 'school', '${school.name}')">
                                    <i class="fas fa-users mr-1.5"></i>
                                    查看用户
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors text-xs"
                                        onclick="confirmDeleteSchool(${school.id}, '${school.name}')">
                                    <i class="fas fa-trash-alt mr-1.5"></i>
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#schoolsContainer').html(html);
        }
        
        // 加载出版社列表
        function loadPublishers() {
            const keyword = $('#searchInput').val();
            const sortField = $('#sortField').val();
            const sortOrder = $('#sortOrder').val();
            
            // 显示加载状态
            $('#publishersContainer').html('<div class="p-6 flex justify-center items-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><span class="ml-2 text-gray-600">加载中...</span></div>');
            
            $.ajax({
                url: '/api/admin/get_all_publishers',
                type: 'GET',
                data: {
                    page: currentPage,
                    page_size: pageSize,
                    keyword: keyword,
                    sort_field: sortField,
                    sort_order: sortOrder
                },
                success: function(response) {
                    if (response.code === 0) {
                        const { total, list, page, page_size, total_pages } = response.data;
                        
                        // 更新计数器
                        $('#publishersCount').text(total);
                        
                        // 更新分页信息
                        currentPage = page;
                        updatePagination(total);
                        
                        // 更新数据列表
                        renderPublishersList(list);
                        
                        // 缓存出版社数据
                        allPublishers = list;
                    } else {
                        showMessage('错误', response.message || '获取出版社列表失败');
                    }
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染出版社列表
        function renderPublishersList(publishers) {
            if (!publishers || publishers.length === 0) {
                $('#publishersContainer').html(`
                    <div class="p-12 text-center">
                        <div class="text-slate-400 mb-4">
                            <i class="fas fa-building text-4xl"></i>
                        </div>
                        <p class="text-slate-500 text-lg">没有找到出版社数据</p>
                        <p class="text-slate-400 text-sm mt-2">请尝试调整搜索条件或添加新的出版社</p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="overflow-hidden">
                    <table class="min-w-full divide-y divide-slate-200">
                        <thead class="bg-slate-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">供应商名称</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">用户数量</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">权限</th>
                                <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100">
            `;

            publishers.forEach(publisher => {
                html += `
                    <tr class="hover:bg-slate-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-building text-purple-600 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900">${publisher.name}</div>
                                    <div class="text-xs text-slate-500">#${publisher.id}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${publisher.user_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${publisher.user_count || 0} 人
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex flex-col items-center gap-1">
                                ${publisher.can_recommend_books ? '<span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">换版推荐</span>' : ''}
                                ${publisher.can_register_exhibition ? '<span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">书展报名</span>' : ''}
                                ${!publisher.can_recommend_books && !publisher.can_register_exhibition ? '<span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">无权限</span>' : ''}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end gap-2">
                                <button class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-xs"
                                        onclick="viewCompanyUsers(${publisher.id}, 'publisher', '${publisher.name}')">
                                    <i class="fas fa-users mr-1.5"></i>
                                    查看用户
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors text-xs"
                                        onclick="editPermissions(${publisher.id}, 'publisher', '${publisher.name}', ${publisher.can_recommend_books || false}, ${publisher.can_register_exhibition || false})">
                                    <i class="fas fa-cog mr-1.5"></i>
                                    权限设置
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors text-xs"
                                        onclick="confirmDeletePublisher(${publisher.id}, '${publisher.name}')">
                                    <i class="fas fa-trash-alt mr-1.5"></i>
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            $('#publishersContainer').html(html);
        }
        
        // 加载经销商列表
        function loadDealers() {
            const keyword = $('#searchInput').val();
            const sortField = $('#sortField').val();
            const sortOrder = $('#sortOrder').val();
            
            // 显示加载状态
            $('#dealersContainer').html('<div class="p-6 flex justify-center items-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><span class="ml-2 text-gray-600">加载中...</span></div>');
            
            $.ajax({
                url: '/api/admin/get_all_dealers',
                type: 'GET',
                data: {
                    page: currentPage,
                    page_size: pageSize,
                    keyword: keyword,
                    sort_field: sortField,
                    sort_order: sortOrder
                },
                success: function(response) {
                    if (response.code === 0) {
                        const { total, list, page, page_size, total_pages } = response.data;
                        
                        // 更新计数器
                        $('#dealersCount').text(total);
                        
                        // 更新分页信息
                        currentPage = page;
                        updatePagination(total);
                        
                        // 更新数据列表
                        renderDealersList(list);
                        
                        // 缓存经销商数据
                        allDealers = list;
                    } else {
                        showMessage('错误', response.message || '获取经销商列表失败');
                    }
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染经销商列表
        function renderDealersList(dealers) {
            if (!dealers || dealers.length === 0) {
                $('#dealersContainer').html(`
                    <div class="p-12 text-center">
                        <div class="text-slate-400 mb-4">
                            <i class="fas fa-store text-4xl"></i>
                        </div>
                        <p class="text-slate-500 text-lg">没有找到经销商数据</p>
                        <p class="text-slate-400 text-sm mt-2">请尝试调整搜索条件或添加新的经销商</p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="overflow-hidden">
                    <table class="min-w-full divide-y divide-slate-200">
                        <thead class="bg-slate-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">经销商名称</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">用户数量</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">权限</th>
                                <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-100">
            `;

            dealers.forEach(dealer => {
                html += `
                    <tr class="hover:bg-slate-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-store text-orange-600 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-900">${dealer.name}</div>
                                    <div class="text-xs text-slate-500">#${dealer.id}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${dealer.user_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${dealer.user_count || 0} 人
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex flex-col items-center gap-1">
                                ${dealer.can_recommend_books ? '<span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">换版推荐</span>' : ''}
                                ${dealer.can_invite_users ? '<span class="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">邀请用户</span>' : ''}
                                ${dealer.can_initiate_exhibition ? '<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">发起书展</span>' : ''}
                                ${dealer.can_register_exhibition ? '<span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">书展报名</span>' : ''}
                                ${!dealer.can_recommend_books && !dealer.can_invite_users && !dealer.can_initiate_exhibition && !dealer.can_register_exhibition ? '<span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">无权限</span>' : ''}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end gap-2">
                                <button class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-xs"
                                        onclick="viewCompanyUsers(${dealer.id}, 'dealer', '${dealer.name}')">
                                    <i class="fas fa-users mr-1.5"></i>
                                    查看用户
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors text-xs"
                                        onclick="editDealerPermissions(${dealer.id}, '${dealer.name}', ${dealer.can_recommend_books || false}, ${dealer.can_invite_users || false}, ${dealer.can_initiate_exhibition || false}, ${dealer.can_register_exhibition || false})">
                                    <i class="fas fa-cog mr-1.5"></i>
                                    权限设置
                                </button>
                                <button class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors text-xs"
                                        onclick="confirmDeleteDealer(${dealer.id}, '${dealer.name}')">
                                    <i class="fas fa-trash-alt mr-1.5"></i>
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            $('#dealersContainer').html(html);
        }

        // 查看公司用户
        function viewCompanyUsers(companyId, companyType, companyName) {
            // 保存当前公司ID和类型，以便删除用户后刷新列表
            currentCompanyId = companyId;
            currentCompanyType = companyType;

            // 显示加载状态
            $('#companyUsersTableBody').html('<tr><td colspan="7" class="px-6 py-12 text-center"><div class="flex justify-center items-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><span class="ml-2 text-slate-600">加载中...</span></div></td></tr>');

            // 更新公司名称和计数
            $('#companyUsersTitle').text(companyName + ' - 用户列表');
            $('#companyUsersCount').text('加载中...');

            // 显示模态框
            $('#viewCompanyUsersModalContainer').removeClass('hidden');

            // 加载公司用户
            $.ajax({
                url: '/api/admin/get_company_users',
                type: 'GET',
                data: {
                    company_id: companyId,
                    company_type: companyType
                },
                success: function(response) {
                    if (response.code === 0) {
                        // 更新用户计数
                        $('#companyUsersCount').text(`共 ${response.data.length} 名用户`);
                        // 渲染用户列表
                        renderCompanyUsers(response.data);
                    } else {
                        $('#companyUsersTableBody').html('<tr><td colspan="7" class="px-6 py-12 text-center text-red-500">获取用户列表失败: ' + (response.message || '未知错误') + '</td></tr>');
                        $('#companyUsersCount').text('获取失败');
                    }
                },
                error: function() {
                    $('#companyUsersTableBody').html('<tr><td colspan="7" class="px-6 py-12 text-center text-red-500">网络错误，请稍后重试</td></tr>');
                    $('#companyUsersCount').text('网络错误');
                }
            });
        }

        // 查看学校详情
        function viewSchoolDetails(schoolId, schoolName, schoolCity, schoolLevel) {
            // 保存当前学校ID
            currentSchoolId = schoolId;

            // 设置表单数据
            $('#schoolDetailsName').val(schoolName);
            $('#schoolDetailsCity').val(schoolCity);
            $('#schoolDetailsLevel').val(schoolLevel);

            // 设置为只读模式
            setSchoolDetailsReadonly(true);

            // 更新标题
            $('#schoolDetailsTitle').text('学校详情');

            // 显示模态框
            $('#schoolDetailsModalContainer').removeClass('hidden');
        }

        // 设置学校详情表单的只读状态
        function setSchoolDetailsReadonly(readonly) {
            console.log('设置表单状态:', readonly ? '只读' : '编辑');

            if (readonly) {
                // 只读模式 - 先清理选择器
                destroyCitySelector();

                $('#schoolDetailsName').prop('readonly', true);
                $('#schoolDetailsCity').prop('readonly', true).show();
                $('#citySelectContainer').hide();
                $('#schoolDetailsLevel').prop('disabled', true);
                $('#editSchoolBtn').show();
                $('#saveSchoolDetailsBtn').hide();
            } else {
                // 编辑模式
                $('#schoolDetailsName').prop('readonly', false);
                $('#schoolDetailsCity').hide();
                $('#citySelectContainer').show();
                $('#schoolDetailsLevel').prop('disabled', false);
                $('#editSchoolBtn').hide();
                $('#saveSchoolDetailsBtn').show();

                // 延迟初始化城市选择器，确保DOM状态正确
                setTimeout(() => {
                    initCitySelector();
                }, 100);
            }
        }

        // 彻底清理城市选择器
        function destroyCitySelector() {
            if (citySelect) {
                try {
                    // 移除所有jQuery事件监听器
                    if (citySelect.container) citySelect.container.off();
                    if (citySelect.trigger) citySelect.trigger.off();
                    if (citySelect.searchInput) citySelect.searchInput.off();
                    if (citySelect.optionsContainer) citySelect.optionsContainer.off();

                    // 移除文档级别的事件监听器
                    $(document).off('click.customSelect');

                    // 重置DOM状态
                    if (citySelect.container) {
                        citySelect.container.removeClass('active');
                        citySelect.container.find('.custom-select-dropdown').hide();
                    }

                    console.log('城市选择器已清理');
                } catch (e) {
                    console.log('清理城市选择器时出错:', e);
                }
                citySelect = null;
            }
            citySelectInitialized = false;
        }

        // 初始化城市选择器
        function initCitySelector() {
            console.log('开始初始化城市选择器...');

            // 如果地址数据还没加载，先加载
            if (!addressData) {
                console.log('地址数据未加载，先加载数据');
                loadAddressData();
                return;
            }

            // 确保容器存在且可见
            const container = $('#citySelectContainer');
            if (!container.length || !container.is(':visible')) {
                console.log('城市选择器容器不存在或不可见，跳过初始化');
                return;
            }

            // 如果已经初始化过，先清理
            if (citySelectInitialized || citySelect) {
                console.log('清理之前的城市选择器实例');
                destroyCitySelector();
            }

            // 重置容器HTML，确保干净的状态
            container.html(`
                <div class="custom-select-trigger">
                    <span class="custom-select-text">请选择城市</span>
                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                </div>
                <div class="custom-select-dropdown">
                    <div class="custom-select-search">
                        <input type="text" placeholder="搜索城市...">
                    </div>
                    <div class="custom-select-options">
                        <!-- 城市选项将动态生成 -->
                    </div>
                </div>
            `);

            // 延迟初始化，确保DOM完全准备好
            setTimeout(() => {
                try {
                    console.log('创建新的城市选择器实例');

                    // 创建新的城市选择器实例
                    citySelect = new CustomSelect('citySelectContainer', {
                        placeholder: '请选择城市',
                        onSelect: function(value, text) {
                            console.log('选择了城市:', value, text);
                        }
                    });

                    // 获取所有城市选项
                    const cityOptions = [];
                    Object.keys(addressData).forEach(provinceId => {
                        const province = addressData[provinceId];
                        Object.keys(province.c).forEach(cityId => {
                            const city = province.c[cityId];
                            cityOptions.push({
                                value: city.n,
                                text: city.n
                            });
                        });
                    });

                    // 去重并排序
                    const uniqueCities = [...new Set(cityOptions.map(item => item.value))]
                        .sort()
                        .map(city => ({value: city, text: city}));

                    citySelect.setOptions(uniqueCities);

                    // 设置当前值
                    const currentCity = $('#schoolDetailsCity').val();
                    if (currentCity) {
                        citySelect.setValue(currentCity);
                    }

                    citySelectInitialized = true;
                    console.log('城市选择器初始化完成，选项数量:', uniqueCities.length);
                } catch (e) {
                    console.error('初始化城市选择器失败:', e);
                    citySelectInitialized = false;
                }
            }, 200);
        }

        // 加载地址数据
        function loadAddressData() {
            if (addressData) {
                console.log('地址数据已存在，跳过加载');
                return;
            }

            console.log('开始加载地址数据...');
            $.ajax({
                url: '/api/teacher/get_address_data',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        addressData = response.data;
                        console.log('地址数据加载成功');

                        // 如果城市选择器容器可见且需要初始化，现在初始化
                        if ($('#citySelectContainer').is(':visible') && !citySelectInitialized) {
                            console.log('地址数据加载完成，初始化城市选择器');
                            initCitySelector();
                        }

                        // 如果添加学校城市选择器容器可见且需要初始化，现在初始化
                        if ($('#addSchoolCitySelectContainer').is(':visible') && !addSchoolCitySelectInitialized) {
                            console.log('地址数据加载完成，初始化添加学校城市选择器');
                            initAddSchoolCitySelector();
                        }
                    } else {
                        console.error('加载地址数据失败:', response.message);
                    }
                },
                error: function() {
                    console.error('加载地址数据时发生网络错误');
                }
            });
        }

        // 彻底清理添加学校城市选择器
        function destroyAddSchoolCitySelector() {
            if (addSchoolCitySelect) {
                try {
                    // 移除所有jQuery事件监听器
                    if (addSchoolCitySelect.container) addSchoolCitySelect.container.off();
                    if (addSchoolCitySelect.trigger) addSchoolCitySelect.trigger.off();
                    if (addSchoolCitySelect.searchInput) addSchoolCitySelect.searchInput.off();
                    if (addSchoolCitySelect.optionsContainer) addSchoolCitySelect.optionsContainer.off();

                    // 移除文档级别的事件监听器
                    $(document).off('click.addSchoolCitySelect');

                    // 重置DOM状态
                    if (addSchoolCitySelect.container) {
                        addSchoolCitySelect.container.removeClass('active');
                        addSchoolCitySelect.container.find('.custom-select-dropdown').hide();
                    }

                    console.log('添加学校城市选择器已清理');
                } catch (e) {
                    console.log('清理添加学校城市选择器时出错:', e);
                }
                addSchoolCitySelect = null;
            }
            addSchoolCitySelectInitialized = false;
        }

        // 初始化添加学校城市选择器
        function initAddSchoolCitySelector() {
            console.log('开始初始化添加学校城市选择器...');

            // 如果地址数据还没加载，先加载
            if (!addressData) {
                console.log('地址数据未加载，先加载数据');
                loadAddressData();
                return;
            }

            // 确保容器存在且可见
            const container = $('#addSchoolCitySelectContainer');
            if (!container.length || !container.is(':visible')) {
                console.log('添加学校城市选择器容器不存在或不可见，跳过初始化');
                return;
            }

            // 如果已经初始化过，先清理
            if (addSchoolCitySelectInitialized || addSchoolCitySelect) {
                console.log('清理之前的添加学校城市选择器实例');
                destroyAddSchoolCitySelector();
            }

            // 重置容器HTML，确保干净的状态
            container.html(`
                <div class="custom-select-trigger">
                    <span class="custom-select-text">请选择城市</span>
                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                </div>
                <div class="custom-select-dropdown">
                    <div class="custom-select-search">
                        <input type="text" placeholder="搜索城市...">
                    </div>
                    <div class="custom-select-options">
                        <!-- 城市选项将动态生成 -->
                    </div>
                </div>
            `);

            // 延迟初始化，确保DOM完全准备好
            setTimeout(() => {
                try {
                    console.log('创建新的添加学校城市选择器实例');

                    // 创建新的城市选择器实例
                    addSchoolCitySelect = new CustomSelect('addSchoolCitySelectContainer', {
                        placeholder: '请选择城市',
                        onSelect: function(value, text) {
                            console.log('选择了城市:', value, text);
                            $('#schoolCity').val(value);
                        }
                    });

                    // 获取所有城市选项
                    const cityOptions = [];
                    Object.keys(addressData).forEach(provinceId => {
                        const province = addressData[provinceId];
                        Object.keys(province.c).forEach(cityId => {
                            const city = province.c[cityId];
                            cityOptions.push({
                                value: city.n,
                                text: city.n
                            });
                        });
                    });

                    // 去重并排序
                    const uniqueCities = [...new Set(cityOptions.map(item => item.value))]
                        .sort()
                        .map(city => ({value: city, text: city}));

                    addSchoolCitySelect.setOptions(uniqueCities);

                    addSchoolCitySelectInitialized = true;
                    console.log('添加学校城市选择器初始化完成，选项数量:', uniqueCities.length);
                } catch (e) {
                    console.error('初始化添加学校城市选择器失败:', e);
                    addSchoolCitySelectInitialized = false;
                }
            }, 200);
        }

        // 渲染公司用户列表
        function renderCompanyUsers(users) {
            if (!users || users.length === 0) {
                $('#companyUsersTableBody').html('<tr><td colspan="7" class="px-6 py-12 text-center text-slate-500">该单位暂无用户</td></tr>');
                return;
            }

            let html = '';

            users.forEach(user => {
                const roles = {
                    'teacher': '教师',
                    'school_teacher': '教师',
                    'publisher': '供应商',
                    'publisher_editor': '供应商',
                    'dealer': '经销商',
                    'dealer_staff': '经销商',
                    'admin': '管理员'
                };

                // 格式化注册时间
                let createTime = '-';
                if (user.create_time) {
                    const date = new Date(user.create_time);
                    createTime = date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
                }

                html += `
                    <tr class="hover:bg-slate-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">${user.username || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-700">${user.name || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-700">${roles[user.role] || user.role}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-700">${user.phone || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-700">${user.email || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-700">${createTime}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors text-xs"
                                    onclick="confirmDeleteUser(${user.id}, '${user.username}', '${user.name || ''}')">
                                <i class="fas fa-trash-alt mr-1.5"></i>
                                删除
                            </button>
                        </td>
                    </tr>
                `;
            });

            $('#companyUsersTableBody').html(html);
        }



        // 编辑出版社权限
        function editPermissions(publisherId, type, name, canRecommendBooks, canRegisterExhibition) {
            // 更新模态框标题
            $('#permissionsModalTitle').text(name + ' - 权限设置');
            
            // 设置复选框状态
            $('#permissionRecommendBooks').prop('checked', canRecommendBooks);
            $('#permissionRegisterExhibition').prop('checked', canRegisterExhibition);
            
            // 设置隐藏字段保存publisherId
            $('#publisherIdForPermissions').val(publisherId);
            
            // 显示模态框
            $('#permissionsModal').removeClass('hidden');
        }

        // 保存出版社权限
        function savePublisherPermissions() {
            const publisherId = $('#publisherIdForPermissions').val();
            const canRecommendBooks = $('#permissionRecommendBooks').prop('checked');
            const canRegisterExhibition = $('#permissionRegisterExhibition').prop('checked');
            
            if (!publisherId) {
                showMessage('错误', '无法获取出版社ID');
                return;
            }
            
            // 显示加载状态
            $('#publisherSavePermissionsBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');
            
            $.ajax({
                url: '/api/admin/update_publisher_permissions',
                type: 'POST',
                data: {
                    publisher_id: publisherId,
                    can_recommend_books: canRecommendBooks,
                    can_register_exhibition: canRegisterExhibition
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '权限设置已更新');
                        $('#permissionsModal').addClass('hidden');
                        loadPublishers(); // 重新加载出版社列表
                    } else {
                        showMessage('错误', response.message || '更新权限失败');
                    }
                    $('#publisherSavePermissionsBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#publisherSavePermissionsBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 关闭权限模态框
        function closePermissionsModal() {
            $('#permissionsModal').addClass('hidden');
        }

        // 编辑经销商权限
        function editDealerPermissions(dealerId, name, canRecommendBooks, canInviteUsers, canInitiateExhibition, canRegisterExhibition) {
            // 更新模态框标题
            $('#dealerPermissionsModalTitle').text(name + ' - 权限设置');
            
            // 设置复选框状态
            $('#dealerPermissionRecommendBooks').prop('checked', canRecommendBooks);
            $('#dealerPermissionInviteUsers').prop('checked', canInviteUsers);
            $('#dealerPermissionInitiateExhibition').prop('checked', canInitiateExhibition);
            $('#dealerPermissionRegisterExhibition').prop('checked', canRegisterExhibition);
            
            // 设置保存按钮的数据属性
            $('#saveDealerPermissionsBtn').data('dealer-id', dealerId);
            
            // 显示模态框
            $('#dealerPermissionsModal').removeClass('hidden');
        }

        // 保存经销商权限
        function saveDealerPermissions(dealerId) {
            const canRecommendBooks = $('#dealerPermissionRecommendBooks').prop('checked');
            const canInviteUsers = $('#dealerPermissionInviteUsers').prop('checked');
            const canInitiateExhibition = $('#dealerPermissionInitiateExhibition').prop('checked');
            const canRegisterExhibition = $('#dealerPermissionRegisterExhibition').prop('checked');
            
            // 显示加载状态
            $('#saveDealerPermissionsBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');
            
            $.ajax({
                url: '/api/admin/update_dealer_permissions',
                type: 'POST',
                data: {
                    dealer_id: dealerId,
                    can_recommend_books: canRecommendBooks,
                    can_invite_users: canInviteUsers,
                    can_initiate_exhibition: canInitiateExhibition,
                    can_register_exhibition: canRegisterExhibition
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '权限设置已更新');
                        $('#dealerPermissionsModal').addClass('hidden');
                        loadDealers(); // 重新加载经销商列表
                    } else {
                        showMessage('错误', response.message || '更新权限失败');
                    }
                    $('#saveDealerPermissionsBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#saveDealerPermissionsBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 关闭经销商权限模态框
        function closeDealerPermissionsModal() {
            $('#dealerPermissionsModal').addClass('hidden');
        }

        // 显示通用提示消息
        function showMessage(title, message) {
            $('#messageModalTitle').text(title);
            $('#messageModalContent').text(message);
            $('#messageModalContainer').removeClass('hidden');
        }

        // 关闭消息模态框
        function closeMessageModal() {
            $('#messageModalContainer').addClass('hidden');
        }

        // 处理分页
        function handlePageChange(page) {
            currentPage = page;
            
            // 根据当前活动的标签页加载数据
            const activeTab = $('.inline-block.border-indigo-500').attr('id');
            if (activeTab === 'schoolsTab') {
                loadSchools();
            } else if (activeTab === 'publishersTab') {
                loadPublishers();
            } else if (activeTab === 'dealersTab') {
                loadDealers();
            }
        }

        // 更新分页信息
        function updatePagination(total) {
            totalPages = Math.ceil(total / pageSize) || 1;

            // 更新显示信息
            $('#currentPage').text(currentPage);
            $('#totalPages').text(totalPages);
            $('#totalCount').text(total);

            // 更新按钮状态
            $('#firstPageBtn').prop('disabled', currentPage <= 1);
            $('#prevPageBtn').prop('disabled', currentPage <= 1);
            $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
            $('#lastPageBtn').prop('disabled', currentPage >= totalPages);

            // 渲染页码
            renderPageNumbers('#paginationNumbers', currentPage, totalPages, function(page) {
                currentPage = page;
                if (currentTab === 'schools') {
                    loadSchools();
                } else if (currentTab === 'publishers') {
                    loadPublishers();
                } else if (currentTab === 'dealers') {
                    loadDealers();
                }
            });
        }

        // 页码生成函数
        function getPageNumbers(currentPage, totalPages) {
            const pageNumbers = [];

            if (totalPages <= 7) {
                // 总页数不超过7页，显示所有页码
                for (let i = 1; i <= totalPages; i++) {
                    pageNumbers.push(i);
                }
            } else {
                // 总页数超过7页，使用省略号
                pageNumbers.push(1);

                if (currentPage <= 4) {
                    // 当前页在前部
                    pageNumbers.push(2, 3, 4, 5);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                    // 当前页在后部
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                    pageNumbers.push(totalPages);
                } else {
                    // 当前页在中部
                    pageNumbers.push('...');
                    pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                }
            }

            return pageNumbers;
        }

        // 渲染页码按钮
        function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
            const container = $(containerSelector);
            container.empty();

            const pageNumbers = getPageNumbers(currentPage, totalPages);

            pageNumbers.forEach(pageNumber => {
                if (pageNumber === '...') {
                    // 省略号
                    container.append(`
                        <span class="relative inline-flex items-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-xl bg-white text-slate-700">
                            ...
                        </span>
                    `);
                } else {
                    // 页码按钮
                    const isActive = pageNumber === currentPage;
                    const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-slate-700 hover:bg-slate-50';

                    container.append(`
                        <button data-page="${pageNumber}"
                                class="relative inline-flex items-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-xl transition-all ${activeClass}">
                            ${pageNumber}
                        </button>
                    `);
                }
            });

            // 绑定页码点击事件
            container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
                const page = parseInt($(this).data('page'));
                if (page && page !== currentPage) {
                    clickHandler(page);
                }
            });
        }

        // 跳转到指定页
        function goToPage(page) {
            currentPage = page;
            if (currentTab === 'schools') {
                loadSchools();
            } else if (currentTab === 'publishers') {
                loadPublishers();
            } else if (currentTab === 'dealers') {
                loadDealers();
            }
        }

        // 加载计数器数据
        function loadCounters() {
            // 加载学校计数
            $.ajax({
                url: '/api/admin/get_all_schools',
                type: 'GET',
                data: { page: 1, page_size: 1 },
                success: function(response) {
                    if (response.code === 0) {
                        $('#schoolsCount').text(response.data.total);
                    }
                }
            });
            
            // 加载出版社计数
            $.ajax({
                url: '/api/admin/get_all_publishers',
                type: 'GET',
                data: { page: 1, page_size: 1 },
                success: function(response) {
                    if (response.code === 0) {
                        $('#publishersCount').text(response.data.total);
                    }
                }
            });
            
            // 加载经销商计数
            $.ajax({
                url: '/api/admin/get_all_dealers',
                type: 'GET',
                data: { page: 1, page_size: 1 },
                success: function(response) {
                    if (response.code === 0) {
                        $('#dealersCount').text(response.data.total);
                    }
                }
            });
        }

        // 初始化上级经销商搜索
        function initParentDealerSearch() {
            $('#parentDealerSearch').on('input', function() {
                const query = $(this).val().trim();
                
                // 如果输入为空，隐藏下拉菜单
                if (query === '') {
                    $('#parentDealerDropdown').removeClass('show');
                    return;
                }
                
                // 显示下拉菜单
                $('#parentDealerDropdown').addClass('show');
                
                // 过滤经销商
                filterParentDealerDropdown(query);
            });
            
            // 点击下拉项时设置值
            $('#parentDealerDropdown').on('click', '.search-dropdown-item', function() {
                const id = $(this).data('id');
                const name = $(this).data('name');
                
                $('#parentDealerId').val(id);
                $('#parentDealerSearch').val(name);
                
                // 隐藏下拉菜单
                $('#parentDealerDropdown').removeClass('show');
            });
            
            // 点击其他区域时隐藏下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-dropdown-menu, #parentDealerSearch').length) {
                    $('#parentDealerDropdown').removeClass('show');
                }
            });
        }
        
        // 初始化上级经销商下拉框
        function initParentDealerDropdown() {
            // 获取所有经销商
            $.ajax({
                url: '/api/admin/get_dealers',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        allDealers = response.data;
                    } else {
                        console.error('获取经销商列表失败:', response.message);
                    }
                },
                error: function() {
                    console.error('网络错误，请稍后重试');
                }
            });
        }
        
        // 过滤上级经销商下拉框
        function filterParentDealerDropdown(query) {
            const dropdown = $('#parentDealerDropdown');
            dropdown.empty();
            
            if (!allDealers || allDealers.length === 0) {
                dropdown.append('<div class="search-dropdown-empty">没有可用的经销商数据</div>');
                return;
            }
            
            // 过滤匹配的经销商
            const filteredDealers = allDealers.filter(dealer => 
                dealer.name.toLowerCase().includes(query.toLowerCase())
            );
            
            if (filteredDealers.length === 0) {
                dropdown.append('<div class="search-dropdown-empty">没有找到匹配的经销商</div>');
                return;
            }
            
            // 显示匹配的经销商
            filteredDealers.forEach(dealer => {
                dropdown.append(`
                    <div class="search-dropdown-item p-2 hover:bg-gray-100 cursor-pointer" data-id="${dealer.id}" data-name="${dealer.name}">
                        ${dealer.name}
                    </div>
                `);
            });
        }

        // 保存学校函数
        function saveSchool() {
            const schoolName = $('#schoolName').val().trim();

            // 获取城市值 - 优先从选择器获取，如果选择器不可用则从隐藏输入框获取
            let schoolCity = '';
            if (addSchoolCitySelect && typeof addSchoolCitySelect.getValue === 'function') {
                try {
                    schoolCity = addSchoolCitySelect.getValue() || '';
                } catch (e) {
                    console.log('从添加学校城市选择器获取值失败:', e);
                    schoolCity = $('#schoolCity').val().trim();
                }
            } else {
                schoolCity = $('#schoolCity').val().trim();
            }

            const schoolLevel = $('#schoolLevel').val();

            // 验证表单
            if (!schoolName) {
                showMessage('错误', '请输入学校名称');
                return;
            }

            // 显示加载状态
            $('#saveSchoolBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');

            // 发送请求
            $.ajax({
                url: '/api/admin/add_school',
                type: 'POST',
                data: {
                    name: schoolName,
                    city: schoolCity,
                    school_level: schoolLevel
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('学校添加成功', 'success');
                        closeAddSchoolModal(); // 使用关闭函数，确保清理
                        loadSchools(); // 重新加载学校列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '添加学校失败');
                    }
                    $('#saveSchoolBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#saveSchoolBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 保存学校详情函数
        function saveSchoolDetails() {
            const schoolName = $('#schoolDetailsName').val().trim();

            // 获取城市值 - 优先从选择器获取，如果选择器不可用则从输入框获取
            let schoolCity = '';
            if (citySelect && typeof citySelect.getValue === 'function') {
                try {
                    schoolCity = citySelect.getValue() || '';
                } catch (e) {
                    console.log('从城市选择器获取值失败:', e);
                    schoolCity = $('#schoolDetailsCity').val().trim();
                }
            } else {
                schoolCity = $('#schoolDetailsCity').val().trim();
            }

            const schoolLevel = $('#schoolDetailsLevel').val();

            // 验证表单
            if (!schoolName) {
                showMessage('错误', '请输入学校名称');
                return;
            }

            // 显示加载状态
            $('#saveSchoolDetailsBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');

            // 发送请求
            $.ajax({
                url: '/api/admin/update_school',
                type: 'POST',
                data: {
                    school_id: currentSchoolId,
                    name: schoolName,
                    city: schoolCity,
                    school_level: schoolLevel
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('学校信息更新成功', 'success');
                        closeSchoolDetailsModal(); // 使用关闭函数，确保清理
                        loadSchools(); // 重新加载学校列表
                    } else {
                        showMessage('错误', response.message || '更新学校信息失败');
                    }
                    $('#saveSchoolDetailsBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#saveSchoolDetailsBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 保存出版社函数
        function savePublisher() {
            const publisherName = $('#publisherName').val().trim();
            const publisherAddress = $('#publisherAddress').val().trim();
            const publisherPhone = $('#publisherPhone').val().trim();
            const isPublisher = $('#isPublisher').prop('checked');
            const canRecommendBooks = $('#canRecommendBooks').prop('checked');
            const canRegisterExhibition = $('#canRegisterExhibition').prop('checked');
            
            // 验证表单
            if (!publisherName) {
                showMessage('错误', '请输入出版社名称');
                return;
            }
            
            // 显示加载状态
            $('#savePublisherBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');
            
            // 发送请求
            $.ajax({
                url: '/api/admin/add_publisher',
                type: 'POST',
                data: {
                    name: publisherName,
                    address: publisherAddress,
                    contact_phone: publisherPhone,
                    is_publisher: isPublisher,
                    can_recommend_books: canRecommendBooks,
                    can_register_exhibition: canRegisterExhibition
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '出版社添加成功');
                        $('#addPublisherModalContainer').addClass('hidden');
                        loadPublishers(); // 重新加载出版社列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '添加出版社失败');
                    }
                    $('#savePublisherBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#savePublisherBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 保存经销商函数
        function saveDealer() {
            const dealerName = $('#dealerName').val().trim();
            const dealerAddress = $('#dealerAddress').val().trim();
            const dealerPhone = $('#dealerPhone').val().trim();
            const parentDealerId = $('#parentDealerId').val();
            const canRecommendBooks = $('#dealerCanRecommendBooks').prop('checked');
            const canInviteUsers = $('#dealerCanInviteUsers').prop('checked');
            const canInitiateExhibition = $('#dealerCanInitiateExhibition').prop('checked');
            const canRegisterExhibition = $('#dealerCanRegisterExhibition').prop('checked');
            
            // 验证表单
            if (!dealerName) {
                showMessage('错误', '请输入经销商名称');
                return;
            }
            
            // 显示加载状态
            $('#saveDealerBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');
            
            // 发送请求
            $.ajax({
                url: '/api/admin/add_dealer',
                type: 'POST',
                data: {
                    name: dealerName,
                    address: dealerAddress,
                    contact_phone: dealerPhone,
                    parent_company_id: parentDealerId || null,
                    can_recommend_books: canRecommendBooks,
                    can_invite_users: canInviteUsers,
                    can_initiate_exhibition: canInitiateExhibition,
                    can_register_exhibition: canRegisterExhibition
                },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '经销商添加成功');
                        $('#addDealerModalContainer').addClass('hidden');
                        loadDealers(); // 重新加载经销商列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '添加经销商失败');
                    }
                    $('#saveDealerBtn').prop('disabled', false).html('保存');
                },
                error: function() {
                    showMessage('错误', '网络错误，请稍后重试');
                    $('#saveDealerBtn').prop('disabled', false).html('保存');
                }
            });
        }

        // 确认删除学校
        function confirmDeleteSchool(schoolId, schoolName) {
            $('#confirmDeleteModalTitle').text('确认删除学校');
            $('#confirmDeleteModalContent').text(`您确定要删除学校"${schoolName}"吗？该操作不可逆！`);
            $('#confirmDeleteModalWarning').text('删除前请确保该学校下没有关联用户或书展活动，否则将无法删除。');
            $('#deleteItemId').val(schoolId);
            $('#deleteItemType').val('school');
            $('#confirmDeleteModal').removeClass('hidden');
        }

        // 确认删除出版社
        function confirmDeletePublisher(publisherId, publisherName) {
            $('#confirmDeleteModalTitle').text('确认删除出版社');
            $('#confirmDeleteModalContent').text(`您确定要删除出版社"${publisherName}"吗？该操作不可逆！`);
            $('#confirmDeleteModalWarning').text('删除前请确保该出版社下没有关联用户，否则将无法删除。');
            $('#deleteItemId').val(publisherId);
            $('#deleteItemType').val('publisher');
            $('#confirmDeleteModal').removeClass('hidden');
        }

        // 确认删除经销商
        function confirmDeleteDealer(dealerId, dealerName) {
            $('#confirmDeleteModalTitle').text('确认删除经销商');
            $('#confirmDeleteModalContent').text(`您确定要删除经销商"${dealerName}"吗？该操作不可逆！`);
            $('#confirmDeleteModalWarning').text('删除前请确保该经销商下没有关联用户，否则将无法删除。');
            $('#deleteItemId').val(dealerId);
            $('#deleteItemType').val('dealer');
            $('#confirmDeleteModal').removeClass('hidden');
        }

        // 删除学校
        function deleteSchool(schoolId) {
            // 显示加载状态
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...');
            
            $.ajax({
                url: '/api/admin/delete_school',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ id: schoolId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '学校删除成功');
                        $('#confirmDeleteModal').addClass('hidden');
                        loadSchools(); // 重新加载学校列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '删除学校失败');
                    }
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                }
            });
        }

        // 删除出版社
        function deletePublisher(publisherId) {
            // 显示加载状态
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...');
            
            $.ajax({
                url: '/api/admin/delete_publisher',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ id: publisherId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '出版社删除成功');
                        $('#confirmDeleteModal').addClass('hidden');
                        loadPublishers(); // 重新加载出版社列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '删除出版社失败');
                    }
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                }
            });
        }

        // 删除经销商
        function deleteDealer(dealerId) {
            // 显示加载状态
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...');
            
            $.ajax({
                url: '/api/admin/delete_dealer',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ id: dealerId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '经销商删除成功');
                        $('#confirmDeleteModal').addClass('hidden');
                        loadDealers(); // 重新加载经销商列表
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '删除经销商失败');
                    }
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                }
            });
        }

        // 确认删除用户
        function confirmDeleteUser(userId, username, name) {
            const displayName = name ? name : username;
            $('#confirmDeleteModalTitle').text('确认删除用户');
            $('#confirmDeleteModalContent').text(`您确定要删除用户"${displayName}"吗？该操作不可逆！`);
            $('#confirmDeleteModalWarning').text('删除用户将会清除该用户的所有相关数据，包括订单、申请等信息。');
            $('#deleteItemId').val(userId);
            $('#deleteItemType').val('user');
            $('#confirmDeleteModal').removeClass('hidden');
        }

        // 删除用户
        function deleteUser(userId) {
            // 显示加载状态
            $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...');
            
            $.ajax({
                url: '/api/admin/delete_user',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ user_id: userId }),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', '用户删除成功');
                        $('#confirmDeleteModal').addClass('hidden');
                        
                        // 重新加载公司用户列表
                        if (currentCompanyId && currentCompanyType) {
                            viewCompanyUsers(currentCompanyId, currentCompanyType);
                        }
                        
                        // 更新计数器
                        loadCounters();
                    } else {
                        showMessage('错误', response.message || '删除用户失败');
                    }
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                    $('#confirmDeleteBtn').prop('disabled', false).html('确认删除');
                }
            });
        }

        // 打开批量设置权限模态框
        function openBatchPermissionModal() {
            // 重置状态
            resetBatchPermissionModal();

            // 根据当前标签页显示对应的权限类型
            if (currentTab === 'publishers') {
                $('#publisherPermissionTypes').removeClass('hidden');
                $('#dealerPermissionTypes').addClass('hidden');
            } else if (currentTab === 'dealers') {
                $('#publisherPermissionTypes').addClass('hidden');
                $('#dealerPermissionTypes').removeClass('hidden');
            }

            // 显示模态框
            $('#batchPermissionModal').removeClass('hidden');

            // 初始化步骤导航
            initializeStepNavigation();

            // 初始化筛选和排序功能
            initializeFilterAndSort();
        }

        // 关闭批量设置权限模态框
        function closeBatchPermissionModal() {
            $('#batchPermissionModal').addClass('hidden');
            resetBatchPermissionModal();
        }

        // 重置模态框状态
        function resetBatchPermissionModal() {
            currentStep = 1;
            selectedPermissionType = '';
            selectedPermissionValue = 'true';
            selectedCompanies = [];
            allCompanies = [];
            filteredCompanies = [];

            // 重置表单
            $('input[name="permission_type"]').prop('checked', false);
            $('#permission_value_true').prop('checked', true);
            $('#companySearchInput').val('');
            $('#selectAllCompanies').prop('checked', false);
            $('#statusFilter').val('all');
            $('#sortBy').val('name');
            $('#sortOrder').val('asc');

            // 重置步骤显示
            showStep(1);
            updateProgressBar();
            updatePreview();

            // 重置按钮状态
            $('#prevStepBtn').prop('disabled', true);
            $('#nextStepBtn').prop('disabled', true);

            // 隐藏单位列表区域
            $('#companiesListSection').addClass('hidden');
        }

        // 初始化步骤导航
        function initializeStepNavigation() {
            // 绑定权限类型选择事件
            $('input[name="permission_type"]').off('change').on('change', function() {
                if ($(this).is(':checked')) {
                    selectedPermissionType = $(this).val();
                    updatePreview();
                    $('#nextStepBtn').prop('disabled', false);
                }
            });

            // 绑定权限值选择事件
            $('input[name="permission_value"]').off('change').on('change', function() {
                selectedPermissionValue = $(this).val();
                updatePreview();
            });

            // 绑定步骤导航按钮
            $('#prevStepBtn').off('click').on('click', function() {
                if (currentStep > 1) {
                    currentStep--;
                    showStep(currentStep);
                    updateProgressBar();
                }
            });

            $('#nextStepBtn').off('click').on('click', function() {
                if (currentStep < 3) {
                    if (validateCurrentStep()) {
                        currentStep++;
                        showStep(currentStep);
                        updateProgressBar();

                        // 如果进入第3步，加载单位列表
                        if (currentStep === 3) {
                            loadCompaniesForPermission();
                        }
                    }
                } else {
                    // 第3步，执行保存
                    saveBatchPermissions();
                }
            });
        }

        // 显示指定步骤
        function showStep(step) {
            // 隐藏所有步骤
            $('.step-content').addClass('hidden');

            // 显示当前步骤
            $(`#step${step}`).removeClass('hidden');

            // 更新按钮状态
            $('#prevStepBtn').prop('disabled', step === 1);

            if (step === 3) {
                $('#nextStepBtn').text('保存设置').prepend('<i class="fas fa-save mr-1"></i>');
                $('#nextStepBtn').prop('disabled', selectedCompanies.length === 0);
            } else {
                $('#nextStepBtn').html('下一步<i class="fas fa-chevron-right ml-1"></i>');
                $('#nextStepBtn').prop('disabled', !validateCurrentStep());
            }
        }

        // 验证当前步骤
        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    return selectedPermissionType !== '';
                case 2:
                    return selectedPermissionValue !== '';
                case 3:
                    return selectedCompanies.length > 0;
                default:
                    return false;
            }
        }

        // 更新进度条
        function updateProgressBar() {
            const progress = (currentStep / 3) * 100;
            $('#progressBar').css('width', `${progress}%`);
            $('#stepIndicator').text(`${currentStep}/3`);
        }

        // 更新预览信息
        function updatePreview() {
            // 更新权限类型预览
            if (selectedPermissionType) {
                const typeText = getPermissionTypeText(selectedPermissionType);
                $('#previewPermissionType').text(typeText);
            } else {
                $('#previewPermissionType').text('请选择权限类型');
            }

            // 更新权限值预览
            if (selectedPermissionValue === 'true') {
                $('#previewPermissionValue').text('允许');
                $('#previewValueIcon').removeClass('bg-red-100 text-red-600').addClass('bg-green-100 text-green-600');
                $('#previewValueIcon i').removeClass('fa-times').addClass('fa-check');
            } else {
                $('#previewPermissionValue').text('禁止');
                $('#previewValueIcon').removeClass('bg-green-100 text-green-600').addClass('bg-red-100 text-red-600');
                $('#previewValueIcon i').removeClass('fa-check').addClass('fa-times');
            }

            // 更新目标单位预览
            if (selectedCompanies.length > 0) {
                $('#previewTargetCount').text(`已选择 ${selectedCompanies.length} 个单位`);
            } else {
                $('#previewTargetCount').text('未选择');
            }
        }

        // 获取权限类型文本
        function getPermissionTypeText(type) {
            const typeMap = {
                'publisher_can_recommend_books': '出版社换版推荐',
                'publisher_can_register_exhibition': '出版社书展报名',
                'dealer_can_recommend_books': '经销商换版推荐',
                'dealer_can_invite_users': '经销商邀请用户',
                'dealer_can_initiate_exhibition': '经销商发起书展',
                'dealer_can_register_exhibition': '经销商书展报名'
            };
            return typeMap[type] || type;
        }

        // 初始化筛选和排序功能
        function initializeFilterAndSort() {
            // 绑定搜索事件
            $('#companySearchInput').off('input').on('input', function() {
                applyFiltersAndSort();
            });

            // 绑定筛选事件
            $('#statusFilter').off('change').on('change', function() {
                applyFiltersAndSort();
            });

            // 绑定排序事件
            $('#sortBy, #sortOrder').off('change').on('change', function() {
                applyFiltersAndSort();
            });

            // 绑定全选事件
            $('#selectAllCompanies').off('change').on('change', function() {
                const isChecked = $(this).prop('checked');
                $('.company-checkbox:visible').prop('checked', isChecked);
                updateSelectedCount();
            });

            // 绑定刷新按钮
            $('#refreshCompaniesBtn').off('click').on('click', function() {
                if (selectedPermissionType) {
                    loadCompaniesForPermission();
                }
            });
        }

        // 应用筛选和排序
        function applyFiltersAndSort() {
            if (allCompanies.length === 0) return;

            const searchTerm = $('#companySearchInput').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const sortBy = $('#sortBy').val();
            const sortOrder = $('#sortOrder').val();

            // 获取权限字段名
            const permissionField = getPermissionFieldName();

            // 筛选
            filteredCompanies = allCompanies.filter(company => {
                // 搜索筛选
                const matchesSearch = company.name.toLowerCase().includes(searchTerm);

                // 状态筛选
                let matchesStatus = true;
                if (statusFilter === 'enabled') {
                    matchesStatus = company[permissionField];
                } else if (statusFilter === 'disabled') {
                    matchesStatus = !company[permissionField];
                }

                return matchesSearch && matchesStatus;
            });

            // 排序
            filteredCompanies.sort((a, b) => {
                let compareValue = 0;

                if (sortBy === 'name') {
                    compareValue = a.name.localeCompare(b.name, 'zh-CN');
                } else if (sortBy === 'status') {
                    const aStatus = a[permissionField] ? 1 : 0;
                    const bStatus = b[permissionField] ? 1 : 0;
                    compareValue = aStatus - bStatus;
                }

                return sortOrder === 'desc' ? -compareValue : compareValue;
            });

            // 重新渲染列表
            renderFilteredCompanies();

            // 更新统计信息
            updateFilterStats();

            // 更新数据统计
            updateDataStats();
        }

        // 获取权限字段名
        function getPermissionFieldName() {
            if (!selectedPermissionType) return '';

            if (selectedPermissionType.startsWith('publisher_')) {
                return selectedPermissionType.replace('publisher_', '');
            } else if (selectedPermissionType.startsWith('dealer_')) {
                return selectedPermissionType.replace('dealer_', '');
            }
            return '';
        }

        // 渲染筛选后的单位列表
        function renderFilteredCompanies() {
            const permissionField = getPermissionFieldName();
            const targetType = selectedPermissionType.startsWith('publisher_') ? 'publisher' : 'dealer';

            let html = '';

            if (filteredCompanies.length === 0) {
                html = `
                    <div class="text-center py-8 text-slate-500">
                        <i class="fas fa-search text-3xl mb-2"></i>
                        <div>没有找到符合条件的单位</div>
                    </div>
                `;
            } else {
                // 表格头部
                html += `
                    <div class="bg-slate-50 border-b border-slate-200 px-4 py-3 flex items-center text-xs font-medium text-slate-600 uppercase tracking-wide">
                        <div class="w-8"></div>
                        <div class="flex-1">单位名称</div>
                        <div class="w-20 text-center">状态</div>
                    </div>
                `;

                // 单位列表
                filteredCompanies.forEach(company => {
                    const isChecked = company[permissionField];
                    const isSelected = selectedCompanies.some(c => c.id === company.id);
                    const checkedAttr = isSelected ? 'checked' : '';
                    const statusBadge = isChecked ?
                        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">已启用</span>' :
                        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">已禁用</span>';

                    html += `
                        <label class="company-item flex items-center px-4 py-3 border-b border-slate-100 hover:bg-blue-50 transition-colors cursor-pointer" data-name="${company.name.toLowerCase()}">
                            <input type="checkbox" class="company-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                                   data-id="${company.id}" data-type="${targetType}" ${checkedAttr}>
                            <div class="ml-3 flex-1 min-w-0">
                                <div class="font-medium text-slate-800 truncate">${company.name}</div>
                                <div class="text-sm text-slate-500 truncate">
                                    ${company.address ? '地址: ' + company.address : ''}
                                    ${company.contact_phone ? (company.address ? ' | ' : '') + '电话: ' + company.contact_phone : ''}
                                </div>
                            </div>
                            <div class="w-20 flex justify-center">
                                ${statusBadge}
                            </div>
                        </label>
                    `;
                });
            }

            $('#companiesContainer').html(`<div class="max-h-96 overflow-y-auto custom-scrollbar">${html}</div>`);

            // 重新绑定复选框事件
            $('.company-checkbox').off('change').on('change', function() {
                updateSelectedCount();
            });
        }

        // 更新筛选统计信息
        function updateFilterStats() {
            $('#filteredCount').text(filteredCompanies.length);
            $('#selectedCountText').text(selectedCompanies.length);

            // 更新全选框状态
            const visibleCheckboxes = $('.company-checkbox:visible');
            const checkedCheckboxes = $('.company-checkbox:visible:checked');

            if (visibleCheckboxes.length > 0 && checkedCheckboxes.length === visibleCheckboxes.length) {
                $('#selectAllCompanies').prop('checked', true).prop('indeterminate', false);
            } else if (checkedCheckboxes.length > 0) {
                $('#selectAllCompanies').prop('checked', false).prop('indeterminate', true);
            } else {
                $('#selectAllCompanies').prop('checked', false).prop('indeterminate', false);
            }
        }

        // 加载单位列表
        function loadCompaniesForPermission() {
            // 显示加载状态
            $('#companiesContainer').html('<div class="flex justify-center items-center h-32"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><span class="ml-2 text-slate-600">加载中...</span></div>');

            // 使用全局变量
            let targetType;

            if (selectedPermissionType && selectedPermissionType.startsWith('publisher_')) {
                targetType = 'publisher';
            } else if (selectedPermissionType && selectedPermissionType.startsWith('dealer_')) {
                targetType = 'dealer';
            } else {
                $('#companiesContainer').html('<div class="text-center py-4 text-red-500">请先选择权限类型</div>');
                return;
            }

            // 显示单位列表区域
            $('#companiesListSection').removeClass('hidden');
            
            // 加载对应类型的单位列表
            if (targetType === 'publisher') {
                // 加载所有出版社
                $.ajax({
                    url: '/api/admin/get_all_publishers',
                    type: 'GET',
                    data: {
                        page: 1,
                        page_size: 1000 // 获取所有出版社
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            // 存储所有数据
                            allCompanies = response.data.list || [];
                            // 应用筛选和排序
                            applyFiltersAndSort();
                        } else {
                            $('#companiesContainer').html('<div class="text-center py-4 text-red-500">获取出版社列表失败: ' + (response.message || '未知错误') + '</div>');
                        }
                    },
                    error: function() {
                        $('#companiesContainer').html('<div class="text-center py-4 text-red-500">网络错误，请稍后重试</div>');
                    }
                });
            } else if (targetType === 'dealer') {
                // 加载所有经销商
                $.ajax({
                    url: '/api/admin/get_all_dealers',
                    type: 'GET',
                    data: {
                        page: 1,
                        page_size: 1000 // 获取所有经销商
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            // 存储所有数据
                            allCompanies = response.data.list || [];
                            // 应用筛选和排序
                            applyFiltersAndSort();
                        } else {
                            $('#companiesContainer').html('<div class="text-center py-4 text-red-500">获取经销商列表失败: ' + (response.message || '未知错误') + '</div>');
                        }
                    },
                    error: function() {
                        $('#companiesContainer').html('<div class="text-center py-4 text-red-500">网络错误，请稍后重试</div>');
                    }
                });
            }
        }





        // 更新选中单位数量
        function updateSelectedCount() {
            // 更新选中的单位数组
            selectedCompanies = $('.company-checkbox:checked').map(function() {
                const $item = $(this).closest('.company-item');
                return {
                    id: $(this).data('id'),
                    type: $(this).data('type'),
                    name: $item.find('.font-medium').text().trim()
                };
            }).get();

            // 更新选中数量显示
            $('#selectedCount').text(selectedCompanies.length);
            $('#selectedCountText').text(selectedCompanies.length);

            // 更新预览
            updatePreview();

            // 更新下一步按钮状态
            if (currentStep === 3) {
                $('#nextStepBtn').prop('disabled', selectedCompanies.length === 0);
            }

            // 更新筛选统计
            updateFilterStats();
        }

        // 更新数据统计
        function updateDataStats() {
            if (allCompanies.length === 0) {
                $('#dataStatsSection').addClass('hidden');
                return;
            }

            const permissionField = getPermissionFieldName();
            const enabledCount = allCompanies.filter(company => company[permissionField]).length;
            const disabledCount = allCompanies.length - enabledCount;

            $('#totalCompaniesCount').text(allCompanies.length);
            $('#enabledCompaniesCount').text(enabledCount);
            $('#disabledCompaniesCount').text(disabledCount);
            $('#filteredCompaniesCount').text(filteredCompanies.length);

            $('#dataStatsSection').removeClass('hidden');
        }

        // 保存批量权限设置
        function saveBatchPermissions() {
            if (selectedCompanies.length === 0) {
                showMessage('警告', '请至少选择一个单位');
                return;
            }
            
            // 使用全局变量
            const permissionValue = selectedPermissionValue === 'true';

            if (!selectedPermissionType) {
                showMessage('警告', '请选择要设置的权限类型');
                return;
            }

            // 根据权限类型确定API端点和参数
            let apiUrl;
            let requestData = {
                permission_value: permissionValue
            };

            if (selectedPermissionType.startsWith('publisher_')) {
                apiUrl = '/api/admin/batch_update_publisher_permissions';
                requestData.publisher_ids = selectedCompanies.filter(c => c.type === 'publisher').map(c => c.id);
                requestData.permission_type = selectedPermissionType.replace('publisher_', '');
            } else if (selectedPermissionType.startsWith('dealer_')) {
                apiUrl = '/api/admin/batch_update_dealer_permissions';
                requestData.dealer_ids = selectedCompanies.filter(c => c.type === 'dealer').map(c => c.id);
                requestData.permission_type = selectedPermissionType.replace('dealer_', '');
            } else {
                showMessage('错误', '无效的权限类型');
                return;
            }

            // 显示加载状态
            $('#nextStepBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 保存中...');
            
            // 发送请求
            $.ajax({
                url: apiUrl,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', response.message || '权限设置已更新');
                        closeBatchPermissionModal();

                        // 根据当前标签页重新加载数据
                        if (currentTab === 'publishers') {
                            loadPublishers();
                        } else if (currentTab === 'dealers') {
                            loadDealers();
                        }
                    } else {
                        showMessage('错误', response.message || '更新权限失败');
                    }
                    $('#nextStepBtn').prop('disabled', false).html('保存设置<i class="fas fa-save ml-1"></i>');
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                    $('#nextStepBtn').prop('disabled', false).html('保存设置<i class="fas fa-save ml-1"></i>');
                }
            });
        }

        // 初始化文件上传处理器
        function initFileUploadHandlers() {
            // 学校文件上传
            $('#schoolImportFile').change(function() {
                const file = this.files[0];
                if (file) {
                    $('#schoolImportFileName').text(file.name);
                    $('#schoolImportFileInfo').removeClass('hidden');
                } else {
                    $('#schoolImportFileInfo').addClass('hidden');
                }
            });
            
            // 出版社文件上传
            $('#publisherImportFile').change(function() {
                const file = this.files[0];
                if (file) {
                    $('#publisherImportFileName').text(file.name);
                    $('#publisherImportFileInfo').removeClass('hidden');
                } else {
                    $('#publisherImportFileInfo').addClass('hidden');
                }
            });
            
            // 经销商文件上传
            $('#dealerImportFile').change(function() {
                const file = this.files[0];
                if (file) {
                    $('#dealerImportFileName').text(file.name);
                    $('#dealerImportFileInfo').removeClass('hidden');
                } else {
                    $('#dealerImportFileInfo').addClass('hidden');
                }
            });
            
            // 拖放功能
            const dropZones = [
                { selector: '#importSchoolForm .mt-1', fileInput: '#schoolImportFile' },
                { selector: '#importPublisherForm .mt-1', fileInput: '#publisherImportFile' },
                { selector: '#importDealerForm .mt-1', fileInput: '#dealerImportFile' }
            ];
            
            dropZones.forEach(zone => {
                const dropZone = $(zone.selector);
                
                dropZone.on('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).addClass('border-blue-500');
                });
                
                dropZone.on('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('border-blue-500');
                });
                
                dropZone.on('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('border-blue-500');
                    
                    const files = e.originalEvent.dataTransfer.files;
                    if (files.length > 0) {
                        const fileInput = $(zone.fileInput);
                        fileInput.prop('files', files);
                        fileInput.trigger('change');
                    }
                });
            });
        }

        // 处理批量导入
        function importOrganizations(type) {
            let fileInputId, modalId, apiUrl;
            
            if (type === 'school') {
                fileInputId = '#schoolImportFile';
                modalId = '#importSchoolModalContainer';
                apiUrl = '/api/admin/import_schools';
            } else if (type === 'publisher') {
                fileInputId = '#publisherImportFile';
                modalId = '#importPublisherModalContainer';
                apiUrl = '/api/admin/import_publishers';
            } else if (type === 'dealer') {
                fileInputId = '#dealerImportFile';
                modalId = '#importDealerModalContainer';
                apiUrl = '/api/admin/import_dealers';
            } else {
                showMessage('错误', '无效的组织类型');
                return;
            }
            
            const fileInput = $(fileInputId)[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                showMessage('警告', '请选择要导入的Excel文件');
                return;
            }
            
            const file = fileInput.files[0];
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                showMessage('警告', '请上传Excel格式的文件 (.xlsx 或 .xls)');
                return;
            }
            
            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示加载状态
            const btnId = `#import${type.charAt(0).toUpperCase() + type.slice(1)}sBtn`;
            const originalText = $(btnId).html();
            $(btnId).prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> 导入中...');
            
            // 发送请求
            $.ajax({
                url: apiUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('成功', `成功导入${response.data.success_count}条记录${response.data.fail_count > 0 ? '，失败'+response.data.fail_count+'条' : ''}`);
                        $(modalId).addClass('hidden');
                        
                        // 重新加载数据
                        if (type === 'school') {
                            loadSchools();
                        } else if (type === 'publisher') {
                            loadPublishers();
                        } else if (type === 'dealer') {
                            loadDealers();
                        }
                        
                        loadCounters(); // 更新计数器
                    } else {
                        showMessage('错误', response.message || '导入失败');
                    }
                },
                error: function(xhr) {
                    let errorMsg = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showMessage('错误', errorMsg);
                },
                complete: function() {
                    $(btnId).prop('disabled', false).html(originalText);
                }
            });
        }
    </script>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>
</body>
</html>